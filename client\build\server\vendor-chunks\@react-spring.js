"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-spring";
exports.ids = ["vendor-chunks/@react-spring"];
exports.modules = {

/***/ "(ssr)/./node_modules/@react-spring/animated/dist/react-spring_animated.modern.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/@react-spring/animated/dist/react-spring_animated.modern.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Animated: () => (/* binding */ Animated),\n/* harmony export */   AnimatedArray: () => (/* binding */ AnimatedArray),\n/* harmony export */   AnimatedObject: () => (/* binding */ AnimatedObject),\n/* harmony export */   AnimatedString: () => (/* binding */ AnimatedString),\n/* harmony export */   AnimatedValue: () => (/* binding */ AnimatedValue),\n/* harmony export */   createHost: () => (/* binding */ createHost),\n/* harmony export */   getAnimated: () => (/* binding */ getAnimated),\n/* harmony export */   getAnimatedType: () => (/* binding */ getAnimatedType),\n/* harmony export */   getPayload: () => (/* binding */ getPayload),\n/* harmony export */   isAnimated: () => (/* binding */ isAnimated),\n/* harmony export */   setAnimated: () => (/* binding */ setAnimated)\n/* harmony export */ });\n/* harmony import */ var _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-spring/shared */ \"(ssr)/./node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// src/Animated.ts\n\nvar $node = Symbol.for(\"Animated:node\");\nvar isAnimated = (value) => !!value && value[$node] === value;\nvar getAnimated = (owner) => owner && owner[$node];\nvar setAnimated = (owner, node) => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.defineHidden)(owner, $node, node);\nvar getPayload = (owner) => owner && owner[$node] && owner[$node].getPayload();\nvar Animated = class {\n  constructor() {\n    setAnimated(this, this);\n  }\n  /** Get every `AnimatedValue` used by this node. */\n  getPayload() {\n    return this.payload || [];\n  }\n};\n\n// src/AnimatedValue.ts\n\nvar AnimatedValue = class extends Animated {\n  constructor(_value) {\n    super();\n    this._value = _value;\n    this.done = true;\n    this.durationProgress = 0;\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.num(this._value)) {\n      this.lastPosition = this._value;\n    }\n  }\n  /** @internal */\n  static create(value) {\n    return new AnimatedValue(value);\n  }\n  getPayload() {\n    return [this];\n  }\n  getValue() {\n    return this._value;\n  }\n  setValue(value, step) {\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.num(value)) {\n      this.lastPosition = value;\n      if (step) {\n        value = Math.round(value / step) * step;\n        if (this.done) {\n          this.lastPosition = value;\n        }\n      }\n    }\n    if (this._value === value) {\n      return false;\n    }\n    this._value = value;\n    return true;\n  }\n  reset() {\n    const { done } = this;\n    this.done = false;\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.num(this._value)) {\n      this.elapsedTime = 0;\n      this.durationProgress = 0;\n      this.lastPosition = this._value;\n      if (done)\n        this.lastVelocity = null;\n      this.v0 = null;\n    }\n  }\n};\n\n// src/AnimatedString.ts\n\nvar AnimatedString = class extends AnimatedValue {\n  constructor(value) {\n    super(0);\n    this._string = null;\n    this._toString = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.createInterpolator)({\n      output: [value, value]\n    });\n  }\n  /** @internal */\n  static create(value) {\n    return new AnimatedString(value);\n  }\n  getValue() {\n    const value = this._string;\n    return value == null ? this._string = this._toString(this._value) : value;\n  }\n  setValue(value) {\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.str(value)) {\n      if (value == this._string) {\n        return false;\n      }\n      this._string = value;\n      this._value = 1;\n    } else if (super.setValue(value)) {\n      this._string = null;\n    } else {\n      return false;\n    }\n    return true;\n  }\n  reset(goal) {\n    if (goal) {\n      this._toString = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.createInterpolator)({\n        output: [this.getValue(), goal]\n      });\n    }\n    this._value = 0;\n    super.reset();\n  }\n};\n\n// src/AnimatedArray.ts\n\n\n// src/AnimatedObject.ts\n\n\n// src/context.ts\nvar TreeContext = { dependencies: null };\n\n// src/AnimatedObject.ts\nvar AnimatedObject = class extends Animated {\n  constructor(source) {\n    super();\n    this.source = source;\n    this.setValue(source);\n  }\n  getValue(animated) {\n    const values = {};\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.eachProp)(this.source, (source, key) => {\n      if (isAnimated(source)) {\n        values[key] = source.getValue(animated);\n      } else if ((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(source)) {\n        values[key] = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(source);\n      } else if (!animated) {\n        values[key] = source;\n      }\n    });\n    return values;\n  }\n  /** Replace the raw object data */\n  setValue(source) {\n    this.source = source;\n    this.payload = this._makePayload(source);\n  }\n  reset() {\n    if (this.payload) {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(this.payload, (node) => node.reset());\n    }\n  }\n  /** Create a payload set. */\n  _makePayload(source) {\n    if (source) {\n      const payload = /* @__PURE__ */ new Set();\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.eachProp)(source, this._addToPayload, payload);\n      return Array.from(payload);\n    }\n  }\n  /** Add to a payload set. */\n  _addToPayload(source) {\n    if (TreeContext.dependencies && (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(source)) {\n      TreeContext.dependencies.add(source);\n    }\n    const payload = getPayload(source);\n    if (payload) {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(payload, (node) => this.add(node));\n    }\n  }\n};\n\n// src/AnimatedArray.ts\nvar AnimatedArray = class extends AnimatedObject {\n  constructor(source) {\n    super(source);\n  }\n  /** @internal */\n  static create(source) {\n    return new AnimatedArray(source);\n  }\n  getValue() {\n    return this.source.map((node) => node.getValue());\n  }\n  setValue(source) {\n    const payload = this.getPayload();\n    if (source.length == payload.length) {\n      return payload.map((node, i) => node.setValue(source[i])).some(Boolean);\n    }\n    super.setValue(source.map(makeAnimated));\n    return true;\n  }\n};\nfunction makeAnimated(value) {\n  const nodeType = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isAnimatedString)(value) ? AnimatedString : AnimatedValue;\n  return nodeType.create(value);\n}\n\n// src/getAnimatedType.ts\n\nfunction getAnimatedType(value) {\n  const parentNode = getAnimated(value);\n  return parentNode ? parentNode.constructor : _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.arr(value) ? AnimatedArray : (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isAnimatedString)(value) ? AnimatedString : AnimatedValue;\n}\n\n// src/createHost.ts\n\n\n// src/withAnimated.tsx\n\n\n\nvar withAnimated = (Component, host) => {\n  const hasInstance = (\n    // Function components must use \"forwardRef\" to avoid being\n    // re-rendered on every animation frame.\n    !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(Component) || Component.prototype && Component.prototype.isReactComponent\n  );\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((givenProps, givenRef) => {\n    const instanceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const ref = hasInstance && // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(\n      (value) => {\n        instanceRef.current = updateRef(givenRef, value);\n      },\n      [givenRef]\n    );\n    const [props, deps] = getAnimatedState(givenProps, host);\n    const forceUpdate = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useForceUpdate)();\n    const callback = () => {\n      const instance = instanceRef.current;\n      if (hasInstance && !instance) {\n        return;\n      }\n      const didUpdate = instance ? host.applyAnimatedValues(instance, props.getValue(true)) : false;\n      if (didUpdate === false) {\n        forceUpdate();\n      }\n    };\n    const observer = new PropsObserver(callback, deps);\n    const observerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(() => {\n      observerRef.current = observer;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(deps, (dep) => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.addFluidObserver)(dep, observer));\n      return () => {\n        if (observerRef.current) {\n          (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(\n            observerRef.current.deps,\n            (dep) => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.removeFluidObserver)(dep, observerRef.current)\n          );\n          _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.cancel(observerRef.current.update);\n        }\n      };\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(callback, []);\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useOnce)(() => () => {\n      const observer2 = observerRef.current;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(observer2.deps, (dep) => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.removeFluidObserver)(dep, observer2));\n    });\n    const usedProps = host.getComponentProps(props.getValue());\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Component, { ...usedProps, ref });\n  });\n};\nvar PropsObserver = class {\n  constructor(update, deps) {\n    this.update = update;\n    this.deps = deps;\n  }\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.write(this.update);\n    }\n  }\n};\nfunction getAnimatedState(props, host) {\n  const dependencies = /* @__PURE__ */ new Set();\n  TreeContext.dependencies = dependencies;\n  if (props.style)\n    props = {\n      ...props,\n      style: host.createAnimatedStyle(props.style)\n    };\n  props = new AnimatedObject(props);\n  TreeContext.dependencies = null;\n  return [props, dependencies];\n}\nfunction updateRef(ref, value) {\n  if (ref) {\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(ref))\n      ref(value);\n    else\n      ref.current = value;\n  }\n  return value;\n}\n\n// src/createHost.ts\nvar cacheKey = Symbol.for(\"AnimatedComponent\");\nvar createHost = (components, {\n  applyAnimatedValues = () => false,\n  createAnimatedStyle = (style) => new AnimatedObject(style),\n  getComponentProps = (props) => props\n} = {}) => {\n  const hostConfig = {\n    applyAnimatedValues,\n    createAnimatedStyle,\n    getComponentProps\n  };\n  const animated = (Component) => {\n    const displayName = getDisplayName(Component) || \"Anonymous\";\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.str(Component)) {\n      Component = animated[Component] || (animated[Component] = withAnimated(Component, hostConfig));\n    } else {\n      Component = Component[cacheKey] || (Component[cacheKey] = withAnimated(Component, hostConfig));\n    }\n    Component.displayName = `Animated(${displayName})`;\n    return Component;\n  };\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.eachProp)(components, (Component, key) => {\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.arr(components)) {\n      key = getDisplayName(Component);\n    }\n    animated[key] = animated(Component);\n  });\n  return {\n    animated\n  };\n};\nvar getDisplayName = (arg) => _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.str(arg) ? arg : arg && _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.str(arg.displayName) ? arg.displayName : _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(arg) && arg.name || null;\n\n//# sourceMappingURL=react-spring_animated.modern.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-spring/animated/dist/react-spring_animated.modern.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-spring/core/dist/react-spring_core.modern.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/@react-spring/core/dist/react-spring_core.modern.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Any: () => (/* reexport safe */ _react_spring_types__WEBPACK_IMPORTED_MODULE_3__.Any),\n/* harmony export */   BailSignal: () => (/* binding */ BailSignal),\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   FrameValue: () => (/* binding */ FrameValue),\n/* harmony export */   Globals: () => (/* reexport safe */ _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals),\n/* harmony export */   Interpolation: () => (/* binding */ Interpolation),\n/* harmony export */   Spring: () => (/* binding */ Spring),\n/* harmony export */   SpringContext: () => (/* binding */ SpringContext),\n/* harmony export */   SpringRef: () => (/* binding */ SpringRef),\n/* harmony export */   SpringValue: () => (/* binding */ SpringValue),\n/* harmony export */   Trail: () => (/* binding */ Trail),\n/* harmony export */   Transition: () => (/* binding */ Transition),\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   createInterpolator: () => (/* reexport safe */ _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.createInterpolator),\n/* harmony export */   easings: () => (/* reexport safe */ _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.easings),\n/* harmony export */   inferTo: () => (/* binding */ inferTo),\n/* harmony export */   interpolate: () => (/* binding */ interpolate),\n/* harmony export */   to: () => (/* binding */ to),\n/* harmony export */   update: () => (/* binding */ update),\n/* harmony export */   useChain: () => (/* binding */ useChain),\n/* harmony export */   useInView: () => (/* binding */ useInView),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* reexport safe */ _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect),\n/* harmony export */   useReducedMotion: () => (/* reexport safe */ _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useReducedMotion),\n/* harmony export */   useResize: () => (/* binding */ useResize),\n/* harmony export */   useScroll: () => (/* binding */ useScroll),\n/* harmony export */   useSpring: () => (/* binding */ useSpring),\n/* harmony export */   useSpringRef: () => (/* binding */ useSpringRef),\n/* harmony export */   useSpringValue: () => (/* binding */ useSpringValue),\n/* harmony export */   useSprings: () => (/* binding */ useSprings),\n/* harmony export */   useTrail: () => (/* binding */ useTrail),\n/* harmony export */   useTransition: () => (/* binding */ useTransition)\n/* harmony export */ });\n/* harmony import */ var _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-spring/shared */ \"(ssr)/./node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_spring_animated__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-spring/animated */ \"(ssr)/./node_modules/@react-spring/animated/dist/react-spring_animated.modern.mjs\");\n/* harmony import */ var _react_spring_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-spring/types */ \"(ssr)/./node_modules/@react-spring/types/dist/react-spring_types.modern.mjs\");\n// src/hooks/useChain.ts\n\n\n// src/helpers.ts\n\nfunction callProp(value, ...args) {\n  return _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(value) ? value(...args) : value;\n}\nvar matchProp = (value, key) => value === true || !!(key && value && (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(value) ? value(key) : (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(value).includes(key)));\nvar resolveProp = (prop, key) => _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(prop) ? key && prop[key] : prop;\nvar getDefaultProp = (props, key) => props.default === true ? props[key] : props.default ? props.default[key] : void 0;\nvar noopTransform = (value) => value;\nvar getDefaultProps = (props, transform = noopTransform) => {\n  let keys = DEFAULT_PROPS;\n  if (props.default && props.default !== true) {\n    props = props.default;\n    keys = Object.keys(props);\n  }\n  const defaults2 = {};\n  for (const key of keys) {\n    const value = transform(props[key], key);\n    if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(value)) {\n      defaults2[key] = value;\n    }\n  }\n  return defaults2;\n};\nvar DEFAULT_PROPS = [\n  \"config\",\n  \"onProps\",\n  \"onStart\",\n  \"onChange\",\n  \"onPause\",\n  \"onResume\",\n  \"onRest\"\n];\nvar RESERVED_PROPS = {\n  config: 1,\n  from: 1,\n  to: 1,\n  ref: 1,\n  loop: 1,\n  reset: 1,\n  pause: 1,\n  cancel: 1,\n  reverse: 1,\n  immediate: 1,\n  default: 1,\n  delay: 1,\n  onProps: 1,\n  onStart: 1,\n  onChange: 1,\n  onPause: 1,\n  onResume: 1,\n  onRest: 1,\n  onResolve: 1,\n  // Transition props\n  items: 1,\n  trail: 1,\n  sort: 1,\n  expires: 1,\n  initial: 1,\n  enter: 1,\n  update: 1,\n  leave: 1,\n  children: 1,\n  onDestroyed: 1,\n  // Internal props\n  keys: 1,\n  callId: 1,\n  parentId: 1\n};\nfunction getForwardProps(props) {\n  const forward = {};\n  let count = 0;\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.eachProp)(props, (value, prop) => {\n    if (!RESERVED_PROPS[prop]) {\n      forward[prop] = value;\n      count++;\n    }\n  });\n  if (count) {\n    return forward;\n  }\n}\nfunction inferTo(props) {\n  const to2 = getForwardProps(props);\n  if (to2) {\n    const out = { to: to2 };\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.eachProp)(props, (val, key) => key in to2 || (out[key] = val));\n    return out;\n  }\n  return { ...props };\n}\nfunction computeGoal(value) {\n  value = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(value);\n  return _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.arr(value) ? value.map(computeGoal) : (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isAnimatedString)(value) ? _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals.createStringInterpolator({\n    range: [0, 1],\n    output: [value, value]\n  })(1) : value;\n}\nfunction hasProps(props) {\n  for (const _ in props)\n    return true;\n  return false;\n}\nfunction isAsyncTo(to2) {\n  return _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(to2) || _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.arr(to2) && _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(to2[0]);\n}\nfunction detachRefs(ctrl, ref) {\n  ctrl.ref?.delete(ctrl);\n  ref?.delete(ctrl);\n}\nfunction replaceRef(ctrl, ref) {\n  if (ref && ctrl.ref !== ref) {\n    ctrl.ref?.delete(ctrl);\n    ref.add(ctrl);\n    ctrl.ref = ref;\n  }\n}\n\n// src/hooks/useChain.ts\nfunction useChain(refs, timeSteps, timeFrame = 1e3) {\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(() => {\n    if (timeSteps) {\n      let prevDelay = 0;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(refs, (ref, i) => {\n        const controllers = ref.current;\n        if (controllers.length) {\n          let delay = timeFrame * timeSteps[i];\n          if (isNaN(delay))\n            delay = prevDelay;\n          else\n            prevDelay = delay;\n          (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(controllers, (ctrl) => {\n            (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(ctrl.queue, (props) => {\n              const memoizedDelayProp = props.delay;\n              props.delay = (key) => delay + callProp(memoizedDelayProp || 0, key);\n            });\n          });\n          ref.start();\n        }\n      });\n    } else {\n      let p = Promise.resolve();\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(refs, (ref) => {\n        const controllers = ref.current;\n        if (controllers.length) {\n          const queues = controllers.map((ctrl) => {\n            const q = ctrl.queue;\n            ctrl.queue = [];\n            return q;\n          });\n          p = p.then(() => {\n            (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(\n              controllers,\n              (ctrl, i) => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(queues[i] || [], (update2) => ctrl.queue.push(update2))\n            );\n            return Promise.all(ref.start());\n          });\n        }\n      });\n    }\n  });\n}\n\n// src/hooks/useSpring.ts\n\n\n// src/hooks/useSprings.ts\n\n\n\n// src/SpringValue.ts\n\n\n\n// src/AnimationConfig.ts\n\n\n// src/constants.ts\nvar config = {\n  default: { tension: 170, friction: 26 },\n  gentle: { tension: 120, friction: 14 },\n  wobbly: { tension: 180, friction: 12 },\n  stiff: { tension: 210, friction: 20 },\n  slow: { tension: 280, friction: 60 },\n  molasses: { tension: 280, friction: 120 }\n};\n\n// src/AnimationConfig.ts\nvar defaults = {\n  ...config.default,\n  mass: 1,\n  damping: 1,\n  easing: _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.easings.linear,\n  clamp: false\n};\nvar AnimationConfig = class {\n  constructor() {\n    /**\n     * The initial velocity of one or more values.\n     *\n     * @default 0\n     */\n    this.velocity = 0;\n    Object.assign(this, defaults);\n  }\n};\nfunction mergeConfig(config2, newConfig, defaultConfig) {\n  if (defaultConfig) {\n    defaultConfig = { ...defaultConfig };\n    sanitizeConfig(defaultConfig, newConfig);\n    newConfig = { ...defaultConfig, ...newConfig };\n  }\n  sanitizeConfig(config2, newConfig);\n  Object.assign(config2, newConfig);\n  for (const key in defaults) {\n    if (config2[key] == null) {\n      config2[key] = defaults[key];\n    }\n  }\n  let { frequency, damping } = config2;\n  const { mass } = config2;\n  if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(frequency)) {\n    if (frequency < 0.01)\n      frequency = 0.01;\n    if (damping < 0)\n      damping = 0;\n    config2.tension = Math.pow(2 * Math.PI / frequency, 2) * mass;\n    config2.friction = 4 * Math.PI * damping * mass / frequency;\n  }\n  return config2;\n}\nfunction sanitizeConfig(config2, props) {\n  if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props.decay)) {\n    config2.duration = void 0;\n  } else {\n    const isTensionConfig = !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props.tension) || !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props.friction);\n    if (isTensionConfig || !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props.frequency) || !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props.damping) || !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props.mass)) {\n      config2.duration = void 0;\n      config2.decay = void 0;\n    }\n    if (isTensionConfig) {\n      config2.frequency = void 0;\n    }\n  }\n}\n\n// src/Animation.ts\nvar emptyArray = [];\nvar Animation = class {\n  constructor() {\n    this.changed = false;\n    this.values = emptyArray;\n    this.toValues = null;\n    this.fromValues = emptyArray;\n    this.config = new AnimationConfig();\n    this.immediate = false;\n  }\n};\n\n// src/scheduleProps.ts\n\nfunction scheduleProps(callId, { key, props, defaultProps, state, actions }) {\n  return new Promise((resolve, reject) => {\n    let delay;\n    let timeout;\n    let cancel = matchProp(props.cancel ?? defaultProps?.cancel, key);\n    if (cancel) {\n      onStart();\n    } else {\n      if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props.pause)) {\n        state.paused = matchProp(props.pause, key);\n      }\n      let pause = defaultProps?.pause;\n      if (pause !== true) {\n        pause = state.paused || matchProp(pause, key);\n      }\n      delay = callProp(props.delay || 0, key);\n      if (pause) {\n        state.resumeQueue.add(onResume);\n        actions.pause();\n      } else {\n        actions.resume();\n        onResume();\n      }\n    }\n    function onPause() {\n      state.resumeQueue.add(onResume);\n      state.timeouts.delete(timeout);\n      timeout.cancel();\n      delay = timeout.time - _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.now();\n    }\n    function onResume() {\n      if (delay > 0 && !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals.skipAnimation) {\n        state.delayed = true;\n        timeout = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.setTimeout(onStart, delay);\n        state.pauseQueue.add(onPause);\n        state.timeouts.add(timeout);\n      } else {\n        onStart();\n      }\n    }\n    function onStart() {\n      if (state.delayed) {\n        state.delayed = false;\n      }\n      state.pauseQueue.delete(onPause);\n      state.timeouts.delete(timeout);\n      if (callId <= (state.cancelId || 0)) {\n        cancel = true;\n      }\n      try {\n        actions.start({ ...props, callId, cancel }, resolve);\n      } catch (err) {\n        reject(err);\n      }\n    }\n  });\n}\n\n// src/runAsync.ts\n\n\n// src/AnimationResult.ts\nvar getCombinedResult = (target, results) => results.length == 1 ? results[0] : results.some((result) => result.cancelled) ? getCancelledResult(target.get()) : results.every((result) => result.noop) ? getNoopResult(target.get()) : getFinishedResult(\n  target.get(),\n  results.every((result) => result.finished)\n);\nvar getNoopResult = (value) => ({\n  value,\n  noop: true,\n  finished: true,\n  cancelled: false\n});\nvar getFinishedResult = (value, finished, cancelled = false) => ({\n  value,\n  finished,\n  cancelled\n});\nvar getCancelledResult = (value) => ({\n  value,\n  cancelled: true,\n  finished: false\n});\n\n// src/runAsync.ts\nfunction runAsync(to2, props, state, target) {\n  const { callId, parentId, onRest } = props;\n  const { asyncTo: prevTo, promise: prevPromise } = state;\n  if (!parentId && to2 === prevTo && !props.reset) {\n    return prevPromise;\n  }\n  return state.promise = (async () => {\n    state.asyncId = callId;\n    state.asyncTo = to2;\n    const defaultProps = getDefaultProps(\n      props,\n      (value, key) => (\n        // The `onRest` prop is only called when the `runAsync` promise is resolved.\n        key === \"onRest\" ? void 0 : value\n      )\n    );\n    let preventBail;\n    let bail;\n    const bailPromise = new Promise(\n      (resolve, reject) => (preventBail = resolve, bail = reject)\n    );\n    const bailIfEnded = (bailSignal) => {\n      const bailResult = (\n        // The `cancel` prop or `stop` method was used.\n        callId <= (state.cancelId || 0) && getCancelledResult(target) || // The async `to` prop was replaced.\n        callId !== state.asyncId && getFinishedResult(target, false)\n      );\n      if (bailResult) {\n        bailSignal.result = bailResult;\n        bail(bailSignal);\n        throw bailSignal;\n      }\n    };\n    const animate = (arg1, arg2) => {\n      const bailSignal = new BailSignal();\n      const skipAnimationSignal = new SkipAnimationSignal();\n      return (async () => {\n        if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals.skipAnimation) {\n          stopAsync(state);\n          skipAnimationSignal.result = getFinishedResult(target, false);\n          bail(skipAnimationSignal);\n          throw skipAnimationSignal;\n        }\n        bailIfEnded(bailSignal);\n        const props2 = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(arg1) ? { ...arg1 } : { ...arg2, to: arg1 };\n        props2.parentId = callId;\n        (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.eachProp)(defaultProps, (value, key) => {\n          if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props2[key])) {\n            props2[key] = value;\n          }\n        });\n        const result2 = await target.start(props2);\n        bailIfEnded(bailSignal);\n        if (state.paused) {\n          await new Promise((resume) => {\n            state.resumeQueue.add(resume);\n          });\n        }\n        return result2;\n      })();\n    };\n    let result;\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals.skipAnimation) {\n      stopAsync(state);\n      return getFinishedResult(target, false);\n    }\n    try {\n      let animating;\n      if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.arr(to2)) {\n        animating = (async (queue) => {\n          for (const props2 of queue) {\n            await animate(props2);\n          }\n        })(to2);\n      } else {\n        animating = Promise.resolve(to2(animate, target.stop.bind(target)));\n      }\n      await Promise.all([animating.then(preventBail), bailPromise]);\n      result = getFinishedResult(target.get(), true, false);\n    } catch (err) {\n      if (err instanceof BailSignal) {\n        result = err.result;\n      } else if (err instanceof SkipAnimationSignal) {\n        result = err.result;\n      } else {\n        throw err;\n      }\n    } finally {\n      if (callId == state.asyncId) {\n        state.asyncId = parentId;\n        state.asyncTo = parentId ? prevTo : void 0;\n        state.promise = parentId ? prevPromise : void 0;\n      }\n    }\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(onRest)) {\n      _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.batchedUpdates(() => {\n        onRest(result, target, target.item);\n      });\n    }\n    return result;\n  })();\n}\nfunction stopAsync(state, cancelId) {\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.flush)(state.timeouts, (t) => t.cancel());\n  state.pauseQueue.clear();\n  state.resumeQueue.clear();\n  state.asyncId = state.asyncTo = state.promise = void 0;\n  if (cancelId)\n    state.cancelId = cancelId;\n}\nvar BailSignal = class extends Error {\n  constructor() {\n    super(\n      \"An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.\"\n    );\n  }\n};\nvar SkipAnimationSignal = class extends Error {\n  constructor() {\n    super(\"SkipAnimationSignal\");\n  }\n};\n\n// src/FrameValue.ts\n\n\nvar isFrameValue = (value) => value instanceof FrameValue;\nvar nextId = 1;\nvar FrameValue = class extends _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.FluidValue {\n  constructor() {\n    super(...arguments);\n    this.id = nextId++;\n    this._priority = 0;\n  }\n  get priority() {\n    return this._priority;\n  }\n  set priority(priority) {\n    if (this._priority != priority) {\n      this._priority = priority;\n      this._onPriorityChange(priority);\n    }\n  }\n  /** Get the current value */\n  get() {\n    const node = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimated)(this);\n    return node && node.getValue();\n  }\n  /** Create a spring that maps our value to another value */\n  to(...args) {\n    return _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals.to(this, args);\n  }\n  /** @deprecated Use the `to` method instead. */\n  interpolate(...args) {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.deprecateInterpolate)();\n    return _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals.to(this, args);\n  }\n  toJSON() {\n    return this.get();\n  }\n  observerAdded(count) {\n    if (count == 1)\n      this._attach();\n  }\n  observerRemoved(count) {\n    if (count == 0)\n      this._detach();\n  }\n  /** Called when the first child is added. */\n  _attach() {\n  }\n  /** Called when the last child is removed. */\n  _detach() {\n  }\n  /** Tell our children about our new value */\n  _onChange(value, idle = false) {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.callFluidObservers)(this, {\n      type: \"change\",\n      parent: this,\n      value,\n      idle\n    });\n  }\n  /** Tell our children about our new priority */\n  _onPriorityChange(priority) {\n    if (!this.idle) {\n      _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.frameLoop.sort(this);\n    }\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.callFluidObservers)(this, {\n      type: \"priority\",\n      parent: this,\n      priority\n    });\n  }\n};\n\n// src/SpringPhase.ts\nvar $P = Symbol.for(\"SpringPhase\");\nvar HAS_ANIMATED = 1;\nvar IS_ANIMATING = 2;\nvar IS_PAUSED = 4;\nvar hasAnimated = (target) => (target[$P] & HAS_ANIMATED) > 0;\nvar isAnimating = (target) => (target[$P] & IS_ANIMATING) > 0;\nvar isPaused = (target) => (target[$P] & IS_PAUSED) > 0;\nvar setActiveBit = (target, active) => active ? target[$P] |= IS_ANIMATING | HAS_ANIMATED : target[$P] &= ~IS_ANIMATING;\nvar setPausedBit = (target, paused) => paused ? target[$P] |= IS_PAUSED : target[$P] &= ~IS_PAUSED;\n\n// src/SpringValue.ts\nvar SpringValue = class extends FrameValue {\n  constructor(arg1, arg2) {\n    super();\n    /** The animation state */\n    this.animation = new Animation();\n    /** Some props have customizable default values */\n    this.defaultProps = {};\n    /** The state for `runAsync` calls */\n    this._state = {\n      paused: false,\n      delayed: false,\n      pauseQueue: /* @__PURE__ */ new Set(),\n      resumeQueue: /* @__PURE__ */ new Set(),\n      timeouts: /* @__PURE__ */ new Set()\n    };\n    /** The promise resolvers of pending `start` calls */\n    this._pendingCalls = /* @__PURE__ */ new Set();\n    /** The counter for tracking `scheduleProps` calls */\n    this._lastCallId = 0;\n    /** The last `scheduleProps` call that changed the `to` prop */\n    this._lastToId = 0;\n    this._memoizedDuration = 0;\n    if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(arg1) || !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(arg2)) {\n      const props = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(arg1) ? { ...arg1 } : { ...arg2, from: arg1 };\n      if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props.default)) {\n        props.default = true;\n      }\n      this.start(props);\n    }\n  }\n  /** Equals true when not advancing on each frame. */\n  get idle() {\n    return !(isAnimating(this) || this._state.asyncTo) || isPaused(this);\n  }\n  get goal() {\n    return (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(this.animation.to);\n  }\n  get velocity() {\n    const node = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimated)(this);\n    return node instanceof _react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.AnimatedValue ? node.lastVelocity || 0 : node.getPayload().map((node2) => node2.lastVelocity || 0);\n  }\n  /**\n   * When true, this value has been animated at least once.\n   */\n  get hasAnimated() {\n    return hasAnimated(this);\n  }\n  /**\n   * When true, this value has an unfinished animation,\n   * which is either active or paused.\n   */\n  get isAnimating() {\n    return isAnimating(this);\n  }\n  /**\n   * When true, all current and future animations are paused.\n   */\n  get isPaused() {\n    return isPaused(this);\n  }\n  /**\n   *\n   *\n   */\n  get isDelayed() {\n    return this._state.delayed;\n  }\n  /** Advance the current animation by a number of milliseconds */\n  advance(dt) {\n    let idle = true;\n    let changed = false;\n    const anim = this.animation;\n    let { toValues } = anim;\n    const { config: config2 } = anim;\n    const payload = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getPayload)(anim.to);\n    if (!payload && (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(anim.to)) {\n      toValues = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(anim.to));\n    }\n    anim.values.forEach((node2, i) => {\n      if (node2.done)\n        return;\n      const to2 = (\n        // Animated strings always go from 0 to 1.\n        node2.constructor == _react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.AnimatedString ? 1 : payload ? payload[i].lastPosition : toValues[i]\n      );\n      let finished = anim.immediate;\n      let position = to2;\n      if (!finished) {\n        position = node2.lastPosition;\n        if (config2.tension <= 0) {\n          node2.done = true;\n          return;\n        }\n        let elapsed = node2.elapsedTime += dt;\n        const from = anim.fromValues[i];\n        const v0 = node2.v0 != null ? node2.v0 : node2.v0 = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.arr(config2.velocity) ? config2.velocity[i] : config2.velocity;\n        let velocity;\n        const precision = config2.precision || (from == to2 ? 5e-3 : Math.min(1, Math.abs(to2 - from) * 1e-3));\n        if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(config2.duration)) {\n          let p = 1;\n          if (config2.duration > 0) {\n            if (this._memoizedDuration !== config2.duration) {\n              this._memoizedDuration = config2.duration;\n              if (node2.durationProgress > 0) {\n                node2.elapsedTime = config2.duration * node2.durationProgress;\n                elapsed = node2.elapsedTime += dt;\n              }\n            }\n            p = (config2.progress || 0) + elapsed / this._memoizedDuration;\n            p = p > 1 ? 1 : p < 0 ? 0 : p;\n            node2.durationProgress = p;\n          }\n          position = from + config2.easing(p) * (to2 - from);\n          velocity = (position - node2.lastPosition) / dt;\n          finished = p == 1;\n        } else if (config2.decay) {\n          const decay = config2.decay === true ? 0.998 : config2.decay;\n          const e = Math.exp(-(1 - decay) * elapsed);\n          position = from + v0 / (1 - decay) * (1 - e);\n          finished = Math.abs(node2.lastPosition - position) <= precision;\n          velocity = v0 * e;\n        } else {\n          velocity = node2.lastVelocity == null ? v0 : node2.lastVelocity;\n          const restVelocity = config2.restVelocity || precision / 10;\n          const bounceFactor = config2.clamp ? 0 : config2.bounce;\n          const canBounce = !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(bounceFactor);\n          const isGrowing = from == to2 ? node2.v0 > 0 : from < to2;\n          let isMoving;\n          let isBouncing = false;\n          const step = 1;\n          const numSteps = Math.ceil(dt / step);\n          for (let n = 0; n < numSteps; ++n) {\n            isMoving = Math.abs(velocity) > restVelocity;\n            if (!isMoving) {\n              finished = Math.abs(to2 - position) <= precision;\n              if (finished) {\n                break;\n              }\n            }\n            if (canBounce) {\n              isBouncing = position == to2 || position > to2 == isGrowing;\n              if (isBouncing) {\n                velocity = -velocity * bounceFactor;\n                position = to2;\n              }\n            }\n            const springForce = -config2.tension * 1e-6 * (position - to2);\n            const dampingForce = -config2.friction * 1e-3 * velocity;\n            const acceleration = (springForce + dampingForce) / config2.mass;\n            velocity = velocity + acceleration * step;\n            position = position + velocity * step;\n          }\n        }\n        node2.lastVelocity = velocity;\n        if (Number.isNaN(position)) {\n          console.warn(`Got NaN while animating:`, this);\n          finished = true;\n        }\n      }\n      if (payload && !payload[i].done) {\n        finished = false;\n      }\n      if (finished) {\n        node2.done = true;\n      } else {\n        idle = false;\n      }\n      if (node2.setValue(position, config2.round)) {\n        changed = true;\n      }\n    });\n    const node = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimated)(this);\n    const currVal = node.getValue();\n    if (idle) {\n      const finalVal = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(anim.to);\n      if ((currVal !== finalVal || changed) && !config2.decay) {\n        node.setValue(finalVal);\n        this._onChange(finalVal);\n      } else if (changed && config2.decay) {\n        this._onChange(currVal);\n      }\n      this._stop();\n    } else if (changed) {\n      this._onChange(currVal);\n    }\n  }\n  /** Set the current value, while stopping the current animation */\n  set(value) {\n    _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.batchedUpdates(() => {\n      this._stop();\n      this._focus(value);\n      this._set(value);\n    });\n    return this;\n  }\n  /**\n   * Freeze the active animation in time, as well as any updates merged\n   * before `resume` is called.\n   */\n  pause() {\n    this._update({ pause: true });\n  }\n  /** Resume the animation if paused. */\n  resume() {\n    this._update({ pause: false });\n  }\n  /** Skip to the end of the current animation. */\n  finish() {\n    if (isAnimating(this)) {\n      const { to: to2, config: config2 } = this.animation;\n      _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.batchedUpdates(() => {\n        this._onStart();\n        if (!config2.decay) {\n          this._set(to2, false);\n        }\n        this._stop();\n      });\n    }\n    return this;\n  }\n  /** Push props into the pending queue. */\n  update(props) {\n    const queue = this.queue || (this.queue = []);\n    queue.push(props);\n    return this;\n  }\n  start(to2, arg2) {\n    let queue;\n    if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(to2)) {\n      queue = [_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(to2) ? to2 : { ...arg2, to: to2 }];\n    } else {\n      queue = this.queue || [];\n      this.queue = [];\n    }\n    return Promise.all(\n      queue.map((props) => {\n        const up = this._update(props);\n        return up;\n      })\n    ).then((results) => getCombinedResult(this, results));\n  }\n  /**\n   * Stop the current animation, and cancel any delayed updates.\n   *\n   * Pass `true` to call `onRest` with `cancelled: true`.\n   */\n  stop(cancel) {\n    const { to: to2 } = this.animation;\n    this._focus(this.get());\n    stopAsync(this._state, cancel && this._lastCallId);\n    _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.batchedUpdates(() => this._stop(to2, cancel));\n    return this;\n  }\n  /** Restart the animation. */\n  reset() {\n    this._update({ reset: true });\n  }\n  /** @internal */\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      this._start();\n    } else if (event.type == \"priority\") {\n      this.priority = event.priority + 1;\n    }\n  }\n  /**\n   * Parse the `to` and `from` range from the given `props` object.\n   *\n   * This also ensures the initial value is available to animated components\n   * during the render phase.\n   */\n  _prepareNode(props) {\n    const key = this.key || \"\";\n    let { to: to2, from } = props;\n    to2 = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(to2) ? to2[key] : to2;\n    if (to2 == null || isAsyncTo(to2)) {\n      to2 = void 0;\n    }\n    from = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(from) ? from[key] : from;\n    if (from == null) {\n      from = void 0;\n    }\n    const range = { to: to2, from };\n    if (!hasAnimated(this)) {\n      if (props.reverse)\n        [to2, from] = [from, to2];\n      from = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(from);\n      if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(from)) {\n        this._set(from);\n      } else if (!(0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimated)(this)) {\n        this._set(to2);\n      }\n    }\n    return range;\n  }\n  /** Every update is processed by this method before merging. */\n  _update({ ...props }, isLoop) {\n    const { key, defaultProps } = this;\n    if (props.default)\n      Object.assign(\n        defaultProps,\n        getDefaultProps(\n          props,\n          (value, prop) => /^on/.test(prop) ? resolveProp(value, key) : value\n        )\n      );\n    mergeActiveFn(this, props, \"onProps\");\n    sendEvent(this, \"onProps\", props, this);\n    const range = this._prepareNode(props);\n    if (Object.isFrozen(this)) {\n      throw Error(\n        \"Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?\"\n      );\n    }\n    const state = this._state;\n    return scheduleProps(++this._lastCallId, {\n      key,\n      props,\n      defaultProps,\n      state,\n      actions: {\n        pause: () => {\n          if (!isPaused(this)) {\n            setPausedBit(this, true);\n            (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.flushCalls)(state.pauseQueue);\n            sendEvent(\n              this,\n              \"onPause\",\n              getFinishedResult(this, checkFinished(this, this.animation.to)),\n              this\n            );\n          }\n        },\n        resume: () => {\n          if (isPaused(this)) {\n            setPausedBit(this, false);\n            if (isAnimating(this)) {\n              this._resume();\n            }\n            (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.flushCalls)(state.resumeQueue);\n            sendEvent(\n              this,\n              \"onResume\",\n              getFinishedResult(this, checkFinished(this, this.animation.to)),\n              this\n            );\n          }\n        },\n        start: this._merge.bind(this, range)\n      }\n    }).then((result) => {\n      if (props.loop && result.finished && !(isLoop && result.noop)) {\n        const nextProps = createLoopUpdate(props);\n        if (nextProps) {\n          return this._update(nextProps, true);\n        }\n      }\n      return result;\n    });\n  }\n  /** Merge props into the current animation */\n  _merge(range, props, resolve) {\n    if (props.cancel) {\n      this.stop(true);\n      return resolve(getCancelledResult(this));\n    }\n    const hasToProp = !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(range.to);\n    const hasFromProp = !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(range.from);\n    if (hasToProp || hasFromProp) {\n      if (props.callId > this._lastToId) {\n        this._lastToId = props.callId;\n      } else {\n        return resolve(getCancelledResult(this));\n      }\n    }\n    const { key, defaultProps, animation: anim } = this;\n    const { to: prevTo, from: prevFrom } = anim;\n    let { to: to2 = prevTo, from = prevFrom } = range;\n    if (hasFromProp && !hasToProp && (!props.default || _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(to2))) {\n      to2 = from;\n    }\n    if (props.reverse)\n      [to2, from] = [from, to2];\n    const hasFromChanged = !(0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isEqual)(from, prevFrom);\n    if (hasFromChanged) {\n      anim.from = from;\n    }\n    from = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(from);\n    const hasToChanged = !(0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isEqual)(to2, prevTo);\n    if (hasToChanged) {\n      this._focus(to2);\n    }\n    const hasAsyncTo = isAsyncTo(props.to);\n    const { config: config2 } = anim;\n    const { decay, velocity } = config2;\n    if (hasToProp || hasFromProp) {\n      config2.velocity = 0;\n    }\n    if (props.config && !hasAsyncTo) {\n      mergeConfig(\n        config2,\n        callProp(props.config, key),\n        // Avoid calling the same \"config\" prop twice.\n        props.config !== defaultProps.config ? callProp(defaultProps.config, key) : void 0\n      );\n    }\n    let node = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimated)(this);\n    if (!node || _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(to2)) {\n      return resolve(getFinishedResult(this, true));\n    }\n    const reset = (\n      // When `reset` is undefined, the `from` prop implies `reset: true`,\n      // except for declarative updates. When `reset` is defined, there\n      // must exist a value to animate from.\n      _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props.reset) ? hasFromProp && !props.default : !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(from) && matchProp(props.reset, key)\n    );\n    const value = reset ? from : this.get();\n    const goal = computeGoal(to2);\n    const isAnimatable = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.num(goal) || _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.arr(goal) || (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isAnimatedString)(goal);\n    const immediate = !hasAsyncTo && (!isAnimatable || matchProp(defaultProps.immediate || props.immediate, key));\n    if (hasToChanged) {\n      const nodeType = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimatedType)(to2);\n      if (nodeType !== node.constructor) {\n        if (immediate) {\n          node = this._set(goal);\n        } else\n          throw Error(\n            `Cannot animate between ${node.constructor.name} and ${nodeType.name}, as the \"to\" prop suggests`\n          );\n      }\n    }\n    const goalType = node.constructor;\n    let started = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(to2);\n    let finished = false;\n    if (!started) {\n      const hasValueChanged = reset || !hasAnimated(this) && hasFromChanged;\n      if (hasToChanged || hasValueChanged) {\n        finished = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isEqual)(computeGoal(value), goal);\n        started = !finished;\n      }\n      if (!(0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isEqual)(anim.immediate, immediate) && !immediate || !(0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isEqual)(config2.decay, decay) || !(0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isEqual)(config2.velocity, velocity)) {\n        started = true;\n      }\n    }\n    if (finished && isAnimating(this)) {\n      if (anim.changed && !reset) {\n        started = true;\n      } else if (!started) {\n        this._stop(prevTo);\n      }\n    }\n    if (!hasAsyncTo) {\n      if (started || (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(prevTo)) {\n        anim.values = node.getPayload();\n        anim.toValues = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(to2) ? null : goalType == _react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.AnimatedString ? [1] : (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(goal);\n      }\n      if (anim.immediate != immediate) {\n        anim.immediate = immediate;\n        if (!immediate && !reset) {\n          this._set(prevTo);\n        }\n      }\n      if (started) {\n        const { onRest } = anim;\n        (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(ACTIVE_EVENTS, (type) => mergeActiveFn(this, props, type));\n        const result = getFinishedResult(this, checkFinished(this, prevTo));\n        (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.flushCalls)(this._pendingCalls, result);\n        this._pendingCalls.add(resolve);\n        if (anim.changed)\n          _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.batchedUpdates(() => {\n            anim.changed = !reset;\n            onRest?.(result, this);\n            if (reset) {\n              callProp(defaultProps.onRest, result);\n            } else {\n              anim.onStart?.(result, this);\n            }\n          });\n      }\n    }\n    if (reset) {\n      this._set(value);\n    }\n    if (hasAsyncTo) {\n      resolve(runAsync(props.to, props, this._state, this));\n    } else if (started) {\n      this._start();\n    } else if (isAnimating(this) && !hasToChanged) {\n      this._pendingCalls.add(resolve);\n    } else {\n      resolve(getNoopResult(value));\n    }\n  }\n  /** Update the `animation.to` value, which might be a `FluidValue` */\n  _focus(value) {\n    const anim = this.animation;\n    if (value !== anim.to) {\n      if ((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidObservers)(this)) {\n        this._detach();\n      }\n      anim.to = value;\n      if ((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidObservers)(this)) {\n        this._attach();\n      }\n    }\n  }\n  _attach() {\n    let priority = 0;\n    const { to: to2 } = this.animation;\n    if ((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(to2)) {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.addFluidObserver)(to2, this);\n      if (isFrameValue(to2)) {\n        priority = to2.priority + 1;\n      }\n    }\n    this.priority = priority;\n  }\n  _detach() {\n    const { to: to2 } = this.animation;\n    if ((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(to2)) {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.removeFluidObserver)(to2, this);\n    }\n  }\n  /**\n   * Update the current value from outside the frameloop,\n   * and return the `Animated` node.\n   */\n  _set(arg, idle = true) {\n    const value = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(arg);\n    if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(value)) {\n      const oldNode = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimated)(this);\n      if (!oldNode || !(0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isEqual)(value, oldNode.getValue())) {\n        const nodeType = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimatedType)(value);\n        if (!oldNode || oldNode.constructor != nodeType) {\n          (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.setAnimated)(this, nodeType.create(value));\n        } else {\n          oldNode.setValue(value);\n        }\n        if (oldNode) {\n          _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.batchedUpdates(() => {\n            this._onChange(value, idle);\n          });\n        }\n      }\n    }\n    return (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimated)(this);\n  }\n  _onStart() {\n    const anim = this.animation;\n    if (!anim.changed) {\n      anim.changed = true;\n      sendEvent(\n        this,\n        \"onStart\",\n        getFinishedResult(this, checkFinished(this, anim.to)),\n        this\n      );\n    }\n  }\n  _onChange(value, idle) {\n    if (!idle) {\n      this._onStart();\n      callProp(this.animation.onChange, value, this);\n    }\n    callProp(this.defaultProps.onChange, value, this);\n    super._onChange(value, idle);\n  }\n  // This method resets the animation state (even if already animating) to\n  // ensure the latest from/to range is used, and it also ensures this spring\n  // is added to the frameloop.\n  _start() {\n    const anim = this.animation;\n    (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimated)(this).reset((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(anim.to));\n    if (!anim.immediate) {\n      anim.fromValues = anim.values.map((node) => node.lastPosition);\n    }\n    if (!isAnimating(this)) {\n      setActiveBit(this, true);\n      if (!isPaused(this)) {\n        this._resume();\n      }\n    }\n  }\n  _resume() {\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals.skipAnimation) {\n      this.finish();\n    } else {\n      _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.frameLoop.start(this);\n    }\n  }\n  /**\n   * Exit the frameloop and notify `onRest` listeners.\n   *\n   * Always wrap `_stop` calls with `batchedUpdates`.\n   */\n  _stop(goal, cancel) {\n    if (isAnimating(this)) {\n      setActiveBit(this, false);\n      const anim = this.animation;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(anim.values, (node) => {\n        node.done = true;\n      });\n      if (anim.toValues) {\n        anim.onChange = anim.onPause = anim.onResume = void 0;\n      }\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.callFluidObservers)(this, {\n        type: \"idle\",\n        parent: this\n      });\n      const result = cancel ? getCancelledResult(this.get()) : getFinishedResult(this.get(), checkFinished(this, goal ?? anim.to));\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.flushCalls)(this._pendingCalls, result);\n      if (anim.changed) {\n        anim.changed = false;\n        sendEvent(this, \"onRest\", result, this);\n      }\n    }\n  }\n};\nfunction checkFinished(target, to2) {\n  const goal = computeGoal(to2);\n  const value = computeGoal(target.get());\n  return (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isEqual)(value, goal);\n}\nfunction createLoopUpdate(props, loop = props.loop, to2 = props.to) {\n  const loopRet = callProp(loop);\n  if (loopRet) {\n    const overrides = loopRet !== true && inferTo(loopRet);\n    const reverse = (overrides || props).reverse;\n    const reset = !overrides || overrides.reset;\n    return createUpdate({\n      ...props,\n      loop,\n      // Avoid updating default props when looping.\n      default: false,\n      // Never loop the `pause` prop.\n      pause: void 0,\n      // For the \"reverse\" prop to loop as expected, the \"to\" prop\n      // must be undefined. The \"reverse\" prop is ignored when the\n      // \"to\" prop is an array or function.\n      to: !reverse || isAsyncTo(to2) ? to2 : void 0,\n      // Ignore the \"from\" prop except on reset.\n      from: reset ? props.from : void 0,\n      reset,\n      // The \"loop\" prop can return a \"useSpring\" props object to\n      // override any of the original props.\n      ...overrides\n    });\n  }\n}\nfunction createUpdate(props) {\n  const { to: to2, from } = props = inferTo(props);\n  const keys = /* @__PURE__ */ new Set();\n  if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(to2))\n    findDefined(to2, keys);\n  if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(from))\n    findDefined(from, keys);\n  props.keys = keys.size ? Array.from(keys) : null;\n  return props;\n}\nfunction declareUpdate(props) {\n  const update2 = createUpdate(props);\n  if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(update2.default)) {\n    update2.default = getDefaultProps(update2);\n  }\n  return update2;\n}\nfunction findDefined(values, keys) {\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.eachProp)(values, (value, key) => value != null && keys.add(key));\n}\nvar ACTIVE_EVENTS = [\n  \"onStart\",\n  \"onRest\",\n  \"onChange\",\n  \"onPause\",\n  \"onResume\"\n];\nfunction mergeActiveFn(target, props, type) {\n  target.animation[type] = props[type] !== getDefaultProp(props, type) ? resolveProp(props[type], target.key) : void 0;\n}\nfunction sendEvent(target, type, ...args) {\n  target.animation[type]?.(...args);\n  target.defaultProps[type]?.(...args);\n}\n\n// src/Controller.ts\n\nvar BATCHED_EVENTS = [\"onStart\", \"onChange\", \"onRest\"];\nvar nextId2 = 1;\nvar Controller = class {\n  constructor(props, flush3) {\n    this.id = nextId2++;\n    /** The animated values */\n    this.springs = {};\n    /** The queue of props passed to the `update` method. */\n    this.queue = [];\n    /** The counter for tracking `scheduleProps` calls */\n    this._lastAsyncId = 0;\n    /** The values currently being animated */\n    this._active = /* @__PURE__ */ new Set();\n    /** The values that changed recently */\n    this._changed = /* @__PURE__ */ new Set();\n    /** Equals false when `onStart` listeners can be called */\n    this._started = false;\n    /** State used by the `runAsync` function */\n    this._state = {\n      paused: false,\n      pauseQueue: /* @__PURE__ */ new Set(),\n      resumeQueue: /* @__PURE__ */ new Set(),\n      timeouts: /* @__PURE__ */ new Set()\n    };\n    /** The event queues that are flushed once per frame maximum */\n    this._events = {\n      onStart: /* @__PURE__ */ new Map(),\n      onChange: /* @__PURE__ */ new Map(),\n      onRest: /* @__PURE__ */ new Map()\n    };\n    this._onFrame = this._onFrame.bind(this);\n    if (flush3) {\n      this._flush = flush3;\n    }\n    if (props) {\n      this.start({ default: true, ...props });\n    }\n  }\n  /**\n   * Equals `true` when no spring values are in the frameloop, and\n   * no async animation is currently active.\n   */\n  get idle() {\n    return !this._state.asyncTo && Object.values(this.springs).every((spring) => {\n      return spring.idle && !spring.isDelayed && !spring.isPaused;\n    });\n  }\n  get item() {\n    return this._item;\n  }\n  set item(item) {\n    this._item = item;\n  }\n  /** Get the current values of our springs */\n  get() {\n    const values = {};\n    this.each((spring, key) => values[key] = spring.get());\n    return values;\n  }\n  /** Set the current values without animating. */\n  set(values) {\n    for (const key in values) {\n      const value = values[key];\n      if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(value)) {\n        this.springs[key].set(value);\n      }\n    }\n  }\n  /** Push an update onto the queue of each value. */\n  update(props) {\n    if (props) {\n      this.queue.push(createUpdate(props));\n    }\n    return this;\n  }\n  /**\n   * Start the queued animations for every spring, and resolve the returned\n   * promise once all queued animations have finished or been cancelled.\n   *\n   * When you pass a queue (instead of nothing), that queue is used instead of\n   * the queued animations added with the `update` method, which are left alone.\n   */\n  start(props) {\n    let { queue } = this;\n    if (props) {\n      queue = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(props).map(createUpdate);\n    } else {\n      this.queue = [];\n    }\n    if (this._flush) {\n      return this._flush(this, queue);\n    }\n    prepareKeys(this, queue);\n    return flushUpdateQueue(this, queue);\n  }\n  /** @internal */\n  stop(arg, keys) {\n    if (arg !== !!arg) {\n      keys = arg;\n    }\n    if (keys) {\n      const springs = this.springs;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(keys), (key) => springs[key].stop(!!arg));\n    } else {\n      stopAsync(this._state, this._lastAsyncId);\n      this.each((spring) => spring.stop(!!arg));\n    }\n    return this;\n  }\n  /** Freeze the active animation in time */\n  pause(keys) {\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(keys)) {\n      this.start({ pause: true });\n    } else {\n      const springs = this.springs;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(keys), (key) => springs[key].pause());\n    }\n    return this;\n  }\n  /** Resume the animation if paused. */\n  resume(keys) {\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(keys)) {\n      this.start({ pause: false });\n    } else {\n      const springs = this.springs;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(keys), (key) => springs[key].resume());\n    }\n    return this;\n  }\n  /** Call a function once per spring value */\n  each(iterator) {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.eachProp)(this.springs, iterator);\n  }\n  /** @internal Called at the end of every animation frame */\n  _onFrame() {\n    const { onStart, onChange, onRest } = this._events;\n    const active = this._active.size > 0;\n    const changed = this._changed.size > 0;\n    if (active && !this._started || changed && !this._started) {\n      this._started = true;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.flush)(onStart, ([onStart2, result]) => {\n        result.value = this.get();\n        onStart2(result, this, this._item);\n      });\n    }\n    const idle = !active && this._started;\n    const values = changed || idle && onRest.size ? this.get() : null;\n    if (changed && onChange.size) {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.flush)(onChange, ([onChange2, result]) => {\n        result.value = values;\n        onChange2(result, this, this._item);\n      });\n    }\n    if (idle) {\n      this._started = false;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.flush)(onRest, ([onRest2, result]) => {\n        result.value = values;\n        onRest2(result, this, this._item);\n      });\n    }\n  }\n  /** @internal */\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      this._changed.add(event.parent);\n      if (!event.idle) {\n        this._active.add(event.parent);\n      }\n    } else if (event.type == \"idle\") {\n      this._active.delete(event.parent);\n    } else\n      return;\n    _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.onFrame(this._onFrame);\n  }\n};\nfunction flushUpdateQueue(ctrl, queue) {\n  return Promise.all(queue.map((props) => flushUpdate(ctrl, props))).then(\n    (results) => getCombinedResult(ctrl, results)\n  );\n}\nasync function flushUpdate(ctrl, props, isLoop) {\n  const { keys, to: to2, from, loop, onRest, onResolve } = props;\n  const defaults2 = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(props.default) && props.default;\n  if (loop) {\n    props.loop = false;\n  }\n  if (to2 === false)\n    props.to = null;\n  if (from === false)\n    props.from = null;\n  const asyncTo = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.arr(to2) || _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(to2) ? to2 : void 0;\n  if (asyncTo) {\n    props.to = void 0;\n    props.onRest = void 0;\n    if (defaults2) {\n      defaults2.onRest = void 0;\n    }\n  } else {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(BATCHED_EVENTS, (key) => {\n      const handler = props[key];\n      if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(handler)) {\n        const queue = ctrl[\"_events\"][key];\n        props[key] = ({ finished, cancelled }) => {\n          const result2 = queue.get(handler);\n          if (result2) {\n            if (!finished)\n              result2.finished = false;\n            if (cancelled)\n              result2.cancelled = true;\n          } else {\n            queue.set(handler, {\n              value: null,\n              finished: finished || false,\n              cancelled: cancelled || false\n            });\n          }\n        };\n        if (defaults2) {\n          defaults2[key] = props[key];\n        }\n      }\n    });\n  }\n  const state = ctrl[\"_state\"];\n  if (props.pause === !state.paused) {\n    state.paused = props.pause;\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.flushCalls)(props.pause ? state.pauseQueue : state.resumeQueue);\n  } else if (state.paused) {\n    props.pause = true;\n  }\n  const promises = (keys || Object.keys(ctrl.springs)).map(\n    (key) => ctrl.springs[key].start(props)\n  );\n  const cancel = props.cancel === true || getDefaultProp(props, \"cancel\") === true;\n  if (asyncTo || cancel && state.asyncId) {\n    promises.push(\n      scheduleProps(++ctrl[\"_lastAsyncId\"], {\n        props,\n        state,\n        actions: {\n          pause: _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.noop,\n          resume: _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.noop,\n          start(props2, resolve) {\n            if (cancel) {\n              stopAsync(state, ctrl[\"_lastAsyncId\"]);\n              resolve(getCancelledResult(ctrl));\n            } else {\n              props2.onRest = onRest;\n              resolve(\n                runAsync(\n                  asyncTo,\n                  props2,\n                  state,\n                  ctrl\n                )\n              );\n            }\n          }\n        }\n      })\n    );\n  }\n  if (state.paused) {\n    await new Promise((resume) => {\n      state.resumeQueue.add(resume);\n    });\n  }\n  const result = getCombinedResult(ctrl, await Promise.all(promises));\n  if (loop && result.finished && !(isLoop && result.noop)) {\n    const nextProps = createLoopUpdate(props, loop, to2);\n    if (nextProps) {\n      prepareKeys(ctrl, [nextProps]);\n      return flushUpdate(ctrl, nextProps, true);\n    }\n  }\n  if (onResolve) {\n    _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.batchedUpdates(() => onResolve(result, ctrl, ctrl.item));\n  }\n  return result;\n}\nfunction getSprings(ctrl, props) {\n  const springs = { ...ctrl.springs };\n  if (props) {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(props), (props2) => {\n      if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props2.keys)) {\n        props2 = createUpdate(props2);\n      }\n      if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(props2.to)) {\n        props2 = { ...props2, to: void 0 };\n      }\n      prepareSprings(springs, props2, (key) => {\n        return createSpring(key);\n      });\n    });\n  }\n  setSprings(ctrl, springs);\n  return springs;\n}\nfunction setSprings(ctrl, springs) {\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.eachProp)(springs, (spring, key) => {\n    if (!ctrl.springs[key]) {\n      ctrl.springs[key] = spring;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.addFluidObserver)(spring, ctrl);\n    }\n  });\n}\nfunction createSpring(key, observer) {\n  const spring = new SpringValue();\n  spring.key = key;\n  if (observer) {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.addFluidObserver)(spring, observer);\n  }\n  return spring;\n}\nfunction prepareSprings(springs, props, create) {\n  if (props.keys) {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(props.keys, (key) => {\n      const spring = springs[key] || (springs[key] = create(key));\n      spring[\"_prepareNode\"](props);\n    });\n  }\n}\nfunction prepareKeys(ctrl, queue) {\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(queue, (props) => {\n    prepareSprings(ctrl.springs, props, (key) => {\n      return createSpring(key, ctrl);\n    });\n  });\n}\n\n// src/SpringContext.tsx\n\n\n\nvar SpringContext = ({\n  children,\n  ...props\n}) => {\n  const inherited = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ctx);\n  const pause = props.pause || !!inherited.pause, immediate = props.immediate || !!inherited.immediate;\n  props = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useMemoOne)(() => ({ pause, immediate }), [pause, immediate]);\n  const { Provider } = ctx;\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Provider, { value: props }, children);\n};\nvar ctx = makeContext(SpringContext, {});\nSpringContext.Provider = ctx.Provider;\nSpringContext.Consumer = ctx.Consumer;\nfunction makeContext(target, init) {\n  Object.assign(target, react__WEBPACK_IMPORTED_MODULE_1__.createContext(init));\n  target.Provider._context = target;\n  target.Consumer._context = target;\n  return target;\n}\n\n// src/SpringRef.ts\n\nvar SpringRef = () => {\n  const current = [];\n  const SpringRef2 = function(props) {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.deprecateDirectCall)();\n    const results = [];\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(current, (ctrl, i) => {\n      if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props)) {\n        results.push(ctrl.start());\n      } else {\n        const update2 = _getProps(props, ctrl, i);\n        if (update2) {\n          results.push(ctrl.start(update2));\n        }\n      }\n    });\n    return results;\n  };\n  SpringRef2.current = current;\n  SpringRef2.add = function(ctrl) {\n    if (!current.includes(ctrl)) {\n      current.push(ctrl);\n    }\n  };\n  SpringRef2.delete = function(ctrl) {\n    const i = current.indexOf(ctrl);\n    if (~i)\n      current.splice(i, 1);\n  };\n  SpringRef2.pause = function() {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(current, (ctrl) => ctrl.pause(...arguments));\n    return this;\n  };\n  SpringRef2.resume = function() {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(current, (ctrl) => ctrl.resume(...arguments));\n    return this;\n  };\n  SpringRef2.set = function(values) {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(current, (ctrl, i) => {\n      const update2 = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(values) ? values(i, ctrl) : values;\n      if (update2) {\n        ctrl.set(update2);\n      }\n    });\n  };\n  SpringRef2.start = function(props) {\n    const results = [];\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(current, (ctrl, i) => {\n      if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props)) {\n        results.push(ctrl.start());\n      } else {\n        const update2 = this._getProps(props, ctrl, i);\n        if (update2) {\n          results.push(ctrl.start(update2));\n        }\n      }\n    });\n    return results;\n  };\n  SpringRef2.stop = function() {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(current, (ctrl) => ctrl.stop(...arguments));\n    return this;\n  };\n  SpringRef2.update = function(props) {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(current, (ctrl, i) => ctrl.update(this._getProps(props, ctrl, i)));\n    return this;\n  };\n  const _getProps = function(arg, ctrl, index) {\n    return _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(arg) ? arg(index, ctrl) : arg;\n  };\n  SpringRef2._getProps = _getProps;\n  return SpringRef2;\n};\n\n// src/hooks/useSprings.ts\nfunction useSprings(length, props, deps) {\n  const propsFn = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(props) && props;\n  if (propsFn && !deps)\n    deps = [];\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(\n    () => propsFn || arguments.length == 3 ? SpringRef() : void 0,\n    []\n  );\n  const layoutId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n  const forceUpdate = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useForceUpdate)();\n  const state = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(\n    () => ({\n      ctrls: [],\n      queue: [],\n      flush(ctrl, updates2) {\n        const springs2 = getSprings(ctrl, updates2);\n        const canFlushSync = layoutId.current > 0 && !state.queue.length && !Object.keys(springs2).some((key) => !ctrl.springs[key]);\n        return canFlushSync ? flushUpdateQueue(ctrl, updates2) : new Promise((resolve) => {\n          setSprings(ctrl, springs2);\n          state.queue.push(() => {\n            resolve(flushUpdateQueue(ctrl, updates2));\n          });\n          forceUpdate();\n        });\n      }\n    }),\n    []\n  );\n  const ctrls = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([...state.ctrls]);\n  const updates = [];\n  const prevLength = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.usePrev)(length) || 0;\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(ctrls.current.slice(length, prevLength), (ctrl) => {\n      detachRefs(ctrl, ref);\n      ctrl.stop(true);\n    });\n    ctrls.current.length = length;\n    declareUpdates(prevLength, length);\n  }, [length]);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {\n    declareUpdates(0, Math.min(prevLength, length));\n  }, deps);\n  function declareUpdates(startIndex, endIndex) {\n    for (let i = startIndex; i < endIndex; i++) {\n      const ctrl = ctrls.current[i] || (ctrls.current[i] = new Controller(null, state.flush));\n      const update2 = propsFn ? propsFn(i, ctrl) : props[i];\n      if (update2) {\n        updates[i] = declareUpdate(update2);\n      }\n    }\n  }\n  const springs = ctrls.current.map((ctrl, i) => getSprings(ctrl, updates[i]));\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SpringContext);\n  const prevContext = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.usePrev)(context);\n  const hasContext = context !== prevContext && hasProps(context);\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(() => {\n    layoutId.current++;\n    state.ctrls = ctrls.current;\n    const { queue } = state;\n    if (queue.length) {\n      state.queue = [];\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(queue, (cb) => cb());\n    }\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(ctrls.current, (ctrl, i) => {\n      ref?.add(ctrl);\n      if (hasContext) {\n        ctrl.start({ default: context });\n      }\n      const update2 = updates[i];\n      if (update2) {\n        replaceRef(ctrl, update2.ref);\n        if (ctrl.ref) {\n          ctrl.queue.push(update2);\n        } else {\n          ctrl.start(update2);\n        }\n      }\n    });\n  });\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useOnce)(() => () => {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(state.ctrls, (ctrl) => ctrl.stop(true));\n  });\n  const values = springs.map((x) => ({ ...x }));\n  return ref ? [values, ref] : values;\n}\n\n// src/hooks/useSpring.ts\nfunction useSpring(props, deps) {\n  const isFn = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(props);\n  const [[values], ref] = useSprings(\n    1,\n    isFn ? props : [props],\n    isFn ? deps || [] : deps\n  );\n  return isFn || arguments.length == 2 ? [values, ref] : values;\n}\n\n// src/hooks/useSpringRef.ts\n\nvar initSpringRef = () => SpringRef();\nvar useSpringRef = () => (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initSpringRef)[0];\n\n// src/hooks/useSpringValue.ts\n\nvar useSpringValue = (initial, props) => {\n  const springValue = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useConstant)(() => new SpringValue(initial, props));\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useOnce)(() => () => {\n    springValue.stop();\n  });\n  return springValue;\n};\n\n// src/hooks/useTrail.ts\n\nfunction useTrail(length, propsArg, deps) {\n  const propsFn = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(propsArg) && propsArg;\n  if (propsFn && !deps)\n    deps = [];\n  let reverse = true;\n  let passedRef = void 0;\n  const result = useSprings(\n    length,\n    (i, ctrl) => {\n      const props = propsFn ? propsFn(i, ctrl) : propsArg;\n      passedRef = props.ref;\n      reverse = reverse && props.reverse;\n      return props;\n    },\n    // Ensure the props function is called when no deps exist.\n    // This works around the 3 argument rule.\n    deps || [{}]\n  );\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(() => {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(result[1].current, (ctrl, i) => {\n      const parent = result[1].current[i + (reverse ? 1 : -1)];\n      replaceRef(ctrl, passedRef);\n      if (ctrl.ref) {\n        if (parent) {\n          ctrl.update({ to: parent.springs });\n        }\n        return;\n      }\n      if (parent) {\n        ctrl.start({ to: parent.springs });\n      } else {\n        ctrl.start();\n      }\n    });\n  }, deps);\n  if (propsFn || arguments.length == 3) {\n    const ref = passedRef ?? result[1];\n    ref[\"_getProps\"] = (propsArg2, ctrl, i) => {\n      const props = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(propsArg2) ? propsArg2(i, ctrl) : propsArg2;\n      if (props) {\n        const parent = ref.current[i + (props.reverse ? 1 : -1)];\n        if (parent)\n          props.to = parent.springs;\n        return props;\n      }\n    };\n    return result;\n  }\n  return result[0];\n}\n\n// src/hooks/useTransition.tsx\n\n\n\nfunction useTransition(data, props, deps) {\n  const propsFn = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(props) && props;\n  const {\n    reset,\n    sort,\n    trail = 0,\n    expires = true,\n    exitBeforeEnter = false,\n    onDestroyed,\n    ref: propsRef,\n    config: propsConfig\n  } = propsFn ? propsFn() : props;\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(\n    () => propsFn || arguments.length == 3 ? SpringRef() : void 0,\n    []\n  );\n  const items = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(data);\n  const transitions = [];\n  const usedTransitions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n  const prevTransitions = reset ? null : usedTransitions.current;\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(() => {\n    usedTransitions.current = transitions;\n  });\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useOnce)(() => {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(transitions, (t) => {\n      ref?.add(t.ctrl);\n      t.ctrl.ref = ref;\n    });\n    return () => {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(usedTransitions.current, (t) => {\n        if (t.expired) {\n          clearTimeout(t.expirationId);\n        }\n        detachRefs(t.ctrl, ref);\n        t.ctrl.stop(true);\n      });\n    };\n  });\n  const keys = getKeys(items, propsFn ? propsFn() : props, prevTransitions);\n  const expired = reset && usedTransitions.current || [];\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(\n    () => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(expired, ({ ctrl, item, key }) => {\n      detachRefs(ctrl, ref);\n      callProp(onDestroyed, item, key);\n    })\n  );\n  const reused = [];\n  if (prevTransitions)\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(prevTransitions, (t, i) => {\n      if (t.expired) {\n        clearTimeout(t.expirationId);\n        expired.push(t);\n      } else {\n        i = reused[i] = keys.indexOf(t.key);\n        if (~i)\n          transitions[i] = t;\n      }\n    });\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(items, (item, i) => {\n    if (!transitions[i]) {\n      transitions[i] = {\n        key: keys[i],\n        item,\n        phase: \"mount\" /* MOUNT */,\n        ctrl: new Controller()\n      };\n      transitions[i].ctrl.item = item;\n    }\n  });\n  if (reused.length) {\n    let i = -1;\n    const { leave } = propsFn ? propsFn() : props;\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(reused, (keyIndex, prevIndex) => {\n      const t = prevTransitions[prevIndex];\n      if (~keyIndex) {\n        i = transitions.indexOf(t);\n        transitions[i] = { ...t, item: items[keyIndex] };\n      } else if (leave) {\n        transitions.splice(++i, 0, t);\n      }\n    });\n  }\n  if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(sort)) {\n    transitions.sort((a, b) => sort(a.item, b.item));\n  }\n  let delay = -trail;\n  const forceUpdate = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useForceUpdate)();\n  const defaultProps = getDefaultProps(props);\n  const changes = /* @__PURE__ */ new Map();\n  const exitingTransitions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(/* @__PURE__ */ new Map());\n  const forceChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(transitions, (t, i) => {\n    const key = t.key;\n    const prevPhase = t.phase;\n    const p = propsFn ? propsFn() : props;\n    let to2;\n    let phase;\n    const propsDelay = callProp(p.delay || 0, key);\n    if (prevPhase == \"mount\" /* MOUNT */) {\n      to2 = p.enter;\n      phase = \"enter\" /* ENTER */;\n    } else {\n      const isLeave = keys.indexOf(key) < 0;\n      if (prevPhase != \"leave\" /* LEAVE */) {\n        if (isLeave) {\n          to2 = p.leave;\n          phase = \"leave\" /* LEAVE */;\n        } else if (to2 = p.update) {\n          phase = \"update\" /* UPDATE */;\n        } else\n          return;\n      } else if (!isLeave) {\n        to2 = p.enter;\n        phase = \"enter\" /* ENTER */;\n      } else\n        return;\n    }\n    to2 = callProp(to2, t.item, i);\n    to2 = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(to2) ? inferTo(to2) : { to: to2 };\n    if (!to2.config) {\n      const config2 = propsConfig || defaultProps.config;\n      to2.config = callProp(config2, t.item, i, phase);\n    }\n    delay += trail;\n    const payload = {\n      ...defaultProps,\n      // we need to add our props.delay value you here.\n      delay: propsDelay + delay,\n      ref: propsRef,\n      immediate: p.immediate,\n      // This prevents implied resets.\n      reset: false,\n      // Merge any phase-specific props.\n      ...to2\n    };\n    if (phase == \"enter\" /* ENTER */ && _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(payload.from)) {\n      const p2 = propsFn ? propsFn() : props;\n      const from = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(p2.initial) || prevTransitions ? p2.from : p2.initial;\n      payload.from = callProp(from, t.item, i);\n    }\n    const { onResolve } = payload;\n    payload.onResolve = (result) => {\n      callProp(onResolve, result);\n      const transitions2 = usedTransitions.current;\n      const t2 = transitions2.find((t3) => t3.key === key);\n      if (!t2)\n        return;\n      if (result.cancelled && t2.phase != \"update\" /* UPDATE */) {\n        return;\n      }\n      if (t2.ctrl.idle) {\n        const idle = transitions2.every((t3) => t3.ctrl.idle);\n        if (t2.phase == \"leave\" /* LEAVE */) {\n          const expiry = callProp(expires, t2.item);\n          if (expiry !== false) {\n            const expiryMs = expiry === true ? 0 : expiry;\n            t2.expired = true;\n            if (!idle && expiryMs > 0) {\n              if (expiryMs <= 2147483647)\n                t2.expirationId = setTimeout(forceUpdate, expiryMs);\n              return;\n            }\n          }\n        }\n        if (idle && transitions2.some((t3) => t3.expired)) {\n          exitingTransitions.current.delete(t2);\n          if (exitBeforeEnter) {\n            forceChange.current = true;\n          }\n          forceUpdate();\n        }\n      }\n    };\n    const springs = getSprings(t.ctrl, payload);\n    if (phase === \"leave\" /* LEAVE */ && exitBeforeEnter) {\n      exitingTransitions.current.set(t, { phase, springs, payload });\n    } else {\n      changes.set(t, { phase, springs, payload });\n    }\n  });\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SpringContext);\n  const prevContext = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.usePrev)(context);\n  const hasContext = context !== prevContext && hasProps(context);\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(() => {\n    if (hasContext) {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(transitions, (t) => {\n        t.ctrl.start({ default: context });\n      });\n    }\n  }, [context]);\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(changes, (_, t) => {\n    if (exitingTransitions.current.size) {\n      const ind = transitions.findIndex((state) => state.key === t.key);\n      transitions.splice(ind, 1);\n    }\n  });\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(\n    () => {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(\n        exitingTransitions.current.size ? exitingTransitions.current : changes,\n        ({ phase, payload }, t) => {\n          const { ctrl } = t;\n          t.phase = phase;\n          ref?.add(ctrl);\n          if (hasContext && phase == \"enter\" /* ENTER */) {\n            ctrl.start({ default: context });\n          }\n          if (payload) {\n            replaceRef(ctrl, payload.ref);\n            if ((ctrl.ref || ref) && !forceChange.current) {\n              ctrl.update(payload);\n            } else {\n              ctrl.start(payload);\n              if (forceChange.current) {\n                forceChange.current = false;\n              }\n            }\n          }\n        }\n      );\n    },\n    reset ? void 0 : deps\n  );\n  const renderTransitions = (render) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, transitions.map((t, i) => {\n    const { springs } = changes.get(t) || t.ctrl;\n    const elem = render({ ...springs }, t.item, t, i);\n    return elem && elem.type ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\n      elem.type,\n      {\n        ...elem.props,\n        key: _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.str(t.key) || _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.num(t.key) ? t.key : t.ctrl.id,\n        ref: elem.ref\n      }\n    ) : elem;\n  }));\n  return ref ? [renderTransitions, ref] : renderTransitions;\n}\nvar nextKey = 1;\nfunction getKeys(items, { key, keys = key }, prevTransitions) {\n  if (keys === null) {\n    const reused = /* @__PURE__ */ new Set();\n    return items.map((item) => {\n      const t = prevTransitions && prevTransitions.find(\n        (t2) => t2.item === item && t2.phase !== \"leave\" /* LEAVE */ && !reused.has(t2)\n      );\n      if (t) {\n        reused.add(t);\n        return t.key;\n      }\n      return nextKey++;\n    });\n  }\n  return _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(keys) ? items : _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(keys) ? items.map(keys) : (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(keys);\n}\n\n// src/hooks/useScroll.ts\n\nvar useScroll = ({\n  container,\n  ...springOptions\n} = {}) => {\n  const [scrollValues, api] = useSpring(\n    () => ({\n      scrollX: 0,\n      scrollY: 0,\n      scrollXProgress: 0,\n      scrollYProgress: 0,\n      ...springOptions\n    }),\n    []\n  );\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(() => {\n    const cleanupScroll = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.onScroll)(\n      ({ x, y }) => {\n        api.start({\n          scrollX: x.current,\n          scrollXProgress: x.progress,\n          scrollY: y.current,\n          scrollYProgress: y.progress\n        });\n      },\n      { container: container?.current || void 0 }\n    );\n    return () => {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(Object.values(scrollValues), (value) => value.stop());\n      cleanupScroll();\n    };\n  }, []);\n  return scrollValues;\n};\n\n// src/hooks/useResize.ts\n\nvar useResize = ({\n  container,\n  ...springOptions\n}) => {\n  const [sizeValues, api] = useSpring(\n    () => ({\n      width: 0,\n      height: 0,\n      ...springOptions\n    }),\n    []\n  );\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(() => {\n    const cleanupScroll = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.onResize)(\n      ({ width, height }) => {\n        api.start({\n          width,\n          height,\n          immediate: sizeValues.width.get() === 0 || sizeValues.height.get() === 0\n        });\n      },\n      { container: container?.current || void 0 }\n    );\n    return () => {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(Object.values(sizeValues), (value) => value.stop());\n      cleanupScroll();\n    };\n  }, []);\n  return sizeValues;\n};\n\n// src/hooks/useInView.ts\n\n\nvar defaultThresholdOptions = {\n  any: 0,\n  all: 1\n};\nfunction useInView(props, args) {\n  const [isInView, setIsInView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n  const propsFn = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(props) && props;\n  const springsProps = propsFn ? propsFn() : {};\n  const { to: to2 = {}, from = {}, ...restSpringProps } = springsProps;\n  const intersectionArguments = propsFn ? args : props;\n  const [springs, api] = useSpring(() => ({ from, ...restSpringProps }), []);\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(() => {\n    const element = ref.current;\n    const {\n      root,\n      once,\n      amount = \"any\",\n      ...restArgs\n    } = intersectionArguments ?? {};\n    if (!element || once && isInView || typeof IntersectionObserver === \"undefined\")\n      return;\n    const activeIntersections = /* @__PURE__ */ new WeakMap();\n    const onEnter = () => {\n      if (to2) {\n        api.start(to2);\n      }\n      setIsInView(true);\n      const cleanup = () => {\n        if (from) {\n          api.start(from);\n        }\n        setIsInView(false);\n      };\n      return once ? void 0 : cleanup;\n    };\n    const handleIntersection = (entries) => {\n      entries.forEach((entry) => {\n        const onLeave = activeIntersections.get(entry.target);\n        if (entry.isIntersecting === Boolean(onLeave)) {\n          return;\n        }\n        if (entry.isIntersecting) {\n          const newOnLeave = onEnter();\n          if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(newOnLeave)) {\n            activeIntersections.set(entry.target, newOnLeave);\n          } else {\n            observer.unobserve(entry.target);\n          }\n        } else if (onLeave) {\n          onLeave();\n          activeIntersections.delete(entry.target);\n        }\n      });\n    };\n    const observer = new IntersectionObserver(handleIntersection, {\n      root: root && root.current || void 0,\n      threshold: typeof amount === \"number\" || Array.isArray(amount) ? amount : defaultThresholdOptions[amount],\n      ...restArgs\n    });\n    observer.observe(element);\n    return () => observer.unobserve(element);\n  }, [intersectionArguments]);\n  if (propsFn) {\n    return [ref, springs];\n  }\n  return [ref, isInView];\n}\n\n// src/components/Spring.tsx\nfunction Spring({ children, ...props }) {\n  return children(useSpring(props));\n}\n\n// src/components/Trail.tsx\n\nfunction Trail({\n  items,\n  children,\n  ...props\n}) {\n  const trails = useTrail(items.length, props);\n  return items.map((item, index) => {\n    const result = children(item, index);\n    return _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(result) ? result(trails[index]) : result;\n  });\n}\n\n// src/components/Transition.tsx\nfunction Transition({\n  items,\n  children,\n  ...props\n}) {\n  return useTransition(items, props)(children);\n}\n\n// src/interpolate.ts\n\n\n// src/Interpolation.ts\n\n\nvar Interpolation = class extends FrameValue {\n  constructor(source, args) {\n    super();\n    this.source = source;\n    /** Equals false when in the frameloop */\n    this.idle = true;\n    /** The inputs which are currently animating */\n    this._active = /* @__PURE__ */ new Set();\n    this.calc = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.createInterpolator)(...args);\n    const value = this._get();\n    const nodeType = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimatedType)(value);\n    (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.setAnimated)(this, nodeType.create(value));\n  }\n  advance(_dt) {\n    const value = this._get();\n    const oldValue = this.get();\n    if (!(0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isEqual)(value, oldValue)) {\n      (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimated)(this).setValue(value);\n      this._onChange(value, this.idle);\n    }\n    if (!this.idle && checkIdle(this._active)) {\n      becomeIdle(this);\n    }\n  }\n  _get() {\n    const inputs = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.arr(this.source) ? this.source.map(_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue) : (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(this.source));\n    return this.calc(...inputs);\n  }\n  _start() {\n    if (this.idle && !checkIdle(this._active)) {\n      this.idle = false;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)((0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getPayload)(this), (node) => {\n        node.done = false;\n      });\n      if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals.skipAnimation) {\n        _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.batchedUpdates(() => this.advance());\n        becomeIdle(this);\n      } else {\n        _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.frameLoop.start(this);\n      }\n    }\n  }\n  // Observe our sources only when we're observed.\n  _attach() {\n    let priority = 1;\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(this.source), (source) => {\n      if ((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(source)) {\n        (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.addFluidObserver)(source, this);\n      }\n      if (isFrameValue(source)) {\n        if (!source.idle) {\n          this._active.add(source);\n        }\n        priority = Math.max(priority, source.priority + 1);\n      }\n    });\n    this.priority = priority;\n    this._start();\n  }\n  // Stop observing our sources once we have no observers.\n  _detach() {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(this.source), (source) => {\n      if ((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(source)) {\n        (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.removeFluidObserver)(source, this);\n      }\n    });\n    this._active.clear();\n    becomeIdle(this);\n  }\n  /** @internal */\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      if (event.idle) {\n        this.advance();\n      } else {\n        this._active.add(event.parent);\n        this._start();\n      }\n    } else if (event.type == \"idle\") {\n      this._active.delete(event.parent);\n    } else if (event.type == \"priority\") {\n      this.priority = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(this.source).reduce(\n        (highest, parent) => Math.max(highest, (isFrameValue(parent) ? parent.priority : 0) + 1),\n        0\n      );\n    }\n  }\n};\nfunction isIdle(source) {\n  return source.idle !== false;\n}\nfunction checkIdle(active) {\n  return !active.size || Array.from(active).every(isIdle);\n}\nfunction becomeIdle(self) {\n  if (!self.idle) {\n    self.idle = true;\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)((0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getPayload)(self), (node) => {\n      node.done = true;\n    });\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.callFluidObservers)(self, {\n      type: \"idle\",\n      parent: self\n    });\n  }\n}\n\n// src/interpolate.ts\nvar to = (source, ...args) => new Interpolation(source, args);\nvar interpolate = (source, ...args) => ((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.deprecateInterpolate)(), new Interpolation(source, args));\n\n// src/globals.ts\n\n_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals.assign({\n  createStringInterpolator: _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.createStringInterpolator,\n  to: (source, args) => new Interpolation(source, args)\n});\nvar update = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.frameLoop.advance;\n\n// src/index.ts\n\n\n\n//# sourceMappingURL=react-spring_core.modern.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-spring/core/dist/react-spring_core.modern.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-spring/rafz/dist/react-spring_rafz.modern.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/@react-spring/rafz/dist/react-spring_rafz.modern.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __raf: () => (/* binding */ __raf),\n/* harmony export */   raf: () => (/* binding */ raf)\n/* harmony export */ });\n// src/index.ts\nvar updateQueue = makeQueue();\nvar raf = (fn) => schedule(fn, updateQueue);\nvar writeQueue = makeQueue();\nraf.write = (fn) => schedule(fn, writeQueue);\nvar onStartQueue = makeQueue();\nraf.onStart = (fn) => schedule(fn, onStartQueue);\nvar onFrameQueue = makeQueue();\nraf.onFrame = (fn) => schedule(fn, onFrameQueue);\nvar onFinishQueue = makeQueue();\nraf.onFinish = (fn) => schedule(fn, onFinishQueue);\nvar timeouts = [];\nraf.setTimeout = (handler, ms) => {\n  const time = raf.now() + ms;\n  const cancel = () => {\n    const i = timeouts.findIndex((t) => t.cancel == cancel);\n    if (~i)\n      timeouts.splice(i, 1);\n    pendingCount -= ~i ? 1 : 0;\n  };\n  const timeout = { time, handler, cancel };\n  timeouts.splice(findTimeout(time), 0, timeout);\n  pendingCount += 1;\n  start();\n  return timeout;\n};\nvar findTimeout = (time) => ~(~timeouts.findIndex((t) => t.time > time) || ~timeouts.length);\nraf.cancel = (fn) => {\n  onStartQueue.delete(fn);\n  onFrameQueue.delete(fn);\n  onFinishQueue.delete(fn);\n  updateQueue.delete(fn);\n  writeQueue.delete(fn);\n};\nraf.sync = (fn) => {\n  sync = true;\n  raf.batchedUpdates(fn);\n  sync = false;\n};\nraf.throttle = (fn) => {\n  let lastArgs;\n  function queuedFn() {\n    try {\n      fn(...lastArgs);\n    } finally {\n      lastArgs = null;\n    }\n  }\n  function throttled(...args) {\n    lastArgs = args;\n    raf.onStart(queuedFn);\n  }\n  throttled.handler = fn;\n  throttled.cancel = () => {\n    onStartQueue.delete(queuedFn);\n    lastArgs = null;\n  };\n  return throttled;\n};\nvar nativeRaf = typeof window != \"undefined\" ? window.requestAnimationFrame : (\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  () => {\n  }\n);\nraf.use = (impl) => nativeRaf = impl;\nraf.now = typeof performance != \"undefined\" ? () => performance.now() : Date.now;\nraf.batchedUpdates = (fn) => fn();\nraf.catch = console.error;\nraf.frameLoop = \"always\";\nraf.advance = () => {\n  if (raf.frameLoop !== \"demand\") {\n    console.warn(\n      \"Cannot call the manual advancement of rafz whilst frameLoop is not set as demand\"\n    );\n  } else {\n    update();\n  }\n};\nvar ts = -1;\nvar pendingCount = 0;\nvar sync = false;\nfunction schedule(fn, queue) {\n  if (sync) {\n    queue.delete(fn);\n    fn(0);\n  } else {\n    queue.add(fn);\n    start();\n  }\n}\nfunction start() {\n  if (ts < 0) {\n    ts = 0;\n    if (raf.frameLoop !== \"demand\") {\n      nativeRaf(loop);\n    }\n  }\n}\nfunction stop() {\n  ts = -1;\n}\nfunction loop() {\n  if (~ts) {\n    nativeRaf(loop);\n    raf.batchedUpdates(update);\n  }\n}\nfunction update() {\n  const prevTs = ts;\n  ts = raf.now();\n  const count = findTimeout(ts);\n  if (count) {\n    eachSafely(timeouts.splice(0, count), (t) => t.handler());\n    pendingCount -= count;\n  }\n  if (!pendingCount) {\n    stop();\n    return;\n  }\n  onStartQueue.flush();\n  updateQueue.flush(prevTs ? Math.min(64, ts - prevTs) : 16.667);\n  onFrameQueue.flush();\n  writeQueue.flush();\n  onFinishQueue.flush();\n}\nfunction makeQueue() {\n  let next = /* @__PURE__ */ new Set();\n  let current = next;\n  return {\n    add(fn) {\n      pendingCount += current == next && !next.has(fn) ? 1 : 0;\n      next.add(fn);\n    },\n    delete(fn) {\n      pendingCount -= current == next && next.has(fn) ? 1 : 0;\n      return next.delete(fn);\n    },\n    flush(arg) {\n      if (current.size) {\n        next = /* @__PURE__ */ new Set();\n        pendingCount -= current.size;\n        eachSafely(current, (fn) => fn(arg) && next.add(fn));\n        pendingCount += next.size;\n        current = next;\n      }\n    }\n  };\n}\nfunction eachSafely(values, each) {\n  values.forEach((value) => {\n    try {\n      each(value);\n    } catch (e) {\n      raf.catch(e);\n    }\n  });\n}\nvar __raf = {\n  /** The number of pending tasks */\n  count() {\n    return pendingCount;\n  },\n  /** Whether there's a raf update loop running */\n  isRunning() {\n    return ts >= 0;\n  },\n  /** Clear internal state. Never call from update loop! */\n  clear() {\n    ts = -1;\n    timeouts = [];\n    onStartQueue = makeQueue();\n    updateQueue = makeQueue();\n    onFrameQueue = makeQueue();\n    writeQueue = makeQueue();\n    onFinishQueue = makeQueue();\n    pendingCount = 0;\n  }\n};\n\n//# sourceMappingURL=react-spring_rafz.modern.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-spring/rafz/dist/react-spring_rafz.modern.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FluidValue: () => (/* binding */ FluidValue),\n/* harmony export */   Globals: () => (/* binding */ globals_exports),\n/* harmony export */   addFluidObserver: () => (/* binding */ addFluidObserver),\n/* harmony export */   callFluidObserver: () => (/* binding */ callFluidObserver),\n/* harmony export */   callFluidObservers: () => (/* binding */ callFluidObservers),\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   colorToRgba: () => (/* binding */ colorToRgba),\n/* harmony export */   colors: () => (/* binding */ colors2),\n/* harmony export */   createInterpolator: () => (/* binding */ createInterpolator),\n/* harmony export */   createStringInterpolator: () => (/* binding */ createStringInterpolator2),\n/* harmony export */   defineHidden: () => (/* binding */ defineHidden),\n/* harmony export */   deprecateDirectCall: () => (/* binding */ deprecateDirectCall),\n/* harmony export */   deprecateInterpolate: () => (/* binding */ deprecateInterpolate),\n/* harmony export */   each: () => (/* binding */ each),\n/* harmony export */   eachProp: () => (/* binding */ eachProp),\n/* harmony export */   easings: () => (/* binding */ easings),\n/* harmony export */   flush: () => (/* binding */ flush),\n/* harmony export */   flushCalls: () => (/* binding */ flushCalls),\n/* harmony export */   frameLoop: () => (/* binding */ frameLoop),\n/* harmony export */   getFluidObservers: () => (/* binding */ getFluidObservers),\n/* harmony export */   getFluidValue: () => (/* binding */ getFluidValue),\n/* harmony export */   hasFluidValue: () => (/* binding */ hasFluidValue),\n/* harmony export */   hex3: () => (/* binding */ hex3),\n/* harmony export */   hex4: () => (/* binding */ hex4),\n/* harmony export */   hex6: () => (/* binding */ hex6),\n/* harmony export */   hex8: () => (/* binding */ hex8),\n/* harmony export */   hsl: () => (/* binding */ hsl),\n/* harmony export */   hsla: () => (/* binding */ hsla),\n/* harmony export */   is: () => (/* binding */ is),\n/* harmony export */   isAnimatedString: () => (/* binding */ isAnimatedString),\n/* harmony export */   isEqual: () => (/* binding */ isEqual),\n/* harmony export */   isSSR: () => (/* binding */ isSSR),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   onResize: () => (/* binding */ onResize),\n/* harmony export */   onScroll: () => (/* binding */ onScroll),\n/* harmony export */   once: () => (/* binding */ once),\n/* harmony export */   prefix: () => (/* binding */ prefix),\n/* harmony export */   raf: () => (/* reexport safe */ _react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf),\n/* harmony export */   removeFluidObserver: () => (/* binding */ removeFluidObserver),\n/* harmony export */   rgb: () => (/* binding */ rgb),\n/* harmony export */   rgba: () => (/* binding */ rgba),\n/* harmony export */   setFluidGetter: () => (/* binding */ setFluidGetter),\n/* harmony export */   toArray: () => (/* binding */ toArray),\n/* harmony export */   useConstant: () => (/* binding */ useConstant),\n/* harmony export */   useForceUpdate: () => (/* binding */ useForceUpdate),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   useMemoOne: () => (/* binding */ useMemoOne),\n/* harmony export */   useOnce: () => (/* binding */ useOnce),\n/* harmony export */   usePrev: () => (/* binding */ usePrev),\n/* harmony export */   useReducedMotion: () => (/* binding */ useReducedMotion)\n/* harmony export */ });\n/* harmony import */ var _react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-spring/rafz */ \"(ssr)/./node_modules/@react-spring/rafz/dist/react-spring_rafz.modern.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n// src/globals.ts\nvar globals_exports = {};\n__export(globals_exports, {\n  assign: () => assign,\n  colors: () => colors,\n  createStringInterpolator: () => createStringInterpolator,\n  skipAnimation: () => skipAnimation,\n  to: () => to,\n  willAdvance: () => willAdvance\n});\n\n\n// src/helpers.ts\nfunction noop() {\n}\nvar defineHidden = (obj, key, value) => Object.defineProperty(obj, key, { value, writable: true, configurable: true });\nvar is = {\n  arr: Array.isArray,\n  obj: (a) => !!a && a.constructor.name === \"Object\",\n  fun: (a) => typeof a === \"function\",\n  str: (a) => typeof a === \"string\",\n  num: (a) => typeof a === \"number\",\n  und: (a) => a === void 0\n};\nfunction isEqual(a, b) {\n  if (is.arr(a)) {\n    if (!is.arr(b) || a.length !== b.length)\n      return false;\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] !== b[i])\n        return false;\n    }\n    return true;\n  }\n  return a === b;\n}\nvar each = (obj, fn) => obj.forEach(fn);\nfunction eachProp(obj, fn, ctx) {\n  if (is.arr(obj)) {\n    for (let i = 0; i < obj.length; i++) {\n      fn.call(ctx, obj[i], `${i}`);\n    }\n    return;\n  }\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      fn.call(ctx, obj[key], key);\n    }\n  }\n}\nvar toArray = (a) => is.und(a) ? [] : is.arr(a) ? a : [a];\nfunction flush(queue, iterator) {\n  if (queue.size) {\n    const items = Array.from(queue);\n    queue.clear();\n    each(items, iterator);\n  }\n}\nvar flushCalls = (queue, ...args) => flush(queue, (fn) => fn(...args));\nvar isSSR = () => typeof window === \"undefined\" || !window.navigator || /ServerSideRendering|^Deno\\//.test(window.navigator.userAgent);\n\n// src/globals.ts\nvar createStringInterpolator;\nvar to;\nvar colors = null;\nvar skipAnimation = false;\nvar willAdvance = noop;\nvar assign = (globals) => {\n  if (globals.to)\n    to = globals.to;\n  if (globals.now)\n    _react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf.now = globals.now;\n  if (globals.colors !== void 0)\n    colors = globals.colors;\n  if (globals.skipAnimation != null)\n    skipAnimation = globals.skipAnimation;\n  if (globals.createStringInterpolator)\n    createStringInterpolator = globals.createStringInterpolator;\n  if (globals.requestAnimationFrame)\n    _react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf.use(globals.requestAnimationFrame);\n  if (globals.batchedUpdates)\n    _react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf.batchedUpdates = globals.batchedUpdates;\n  if (globals.willAdvance)\n    willAdvance = globals.willAdvance;\n  if (globals.frameLoop)\n    _react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf.frameLoop = globals.frameLoop;\n};\n\n// src/FrameLoop.ts\n\nvar startQueue = /* @__PURE__ */ new Set();\nvar currentFrame = [];\nvar prevFrame = [];\nvar priority = 0;\nvar frameLoop = {\n  get idle() {\n    return !startQueue.size && !currentFrame.length;\n  },\n  /** Advance the given animation on every frame until idle. */\n  start(animation) {\n    if (priority > animation.priority) {\n      startQueue.add(animation);\n      _react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf.onStart(flushStartQueue);\n    } else {\n      startSafely(animation);\n      (0,_react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf)(advance);\n    }\n  },\n  /** Advance all animations by the given time. */\n  advance,\n  /** Call this when an animation's priority changes. */\n  sort(animation) {\n    if (priority) {\n      _react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf.onFrame(() => frameLoop.sort(animation));\n    } else {\n      const prevIndex = currentFrame.indexOf(animation);\n      if (~prevIndex) {\n        currentFrame.splice(prevIndex, 1);\n        startUnsafely(animation);\n      }\n    }\n  },\n  /**\n   * Clear all animations. For testing purposes.\n   *\n   * ☠️ Never call this from within the frameloop.\n   */\n  clear() {\n    currentFrame = [];\n    startQueue.clear();\n  }\n};\nfunction flushStartQueue() {\n  startQueue.forEach(startSafely);\n  startQueue.clear();\n  (0,_react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf)(advance);\n}\nfunction startSafely(animation) {\n  if (!currentFrame.includes(animation))\n    startUnsafely(animation);\n}\nfunction startUnsafely(animation) {\n  currentFrame.splice(\n    findIndex(currentFrame, (other) => other.priority > animation.priority),\n    0,\n    animation\n  );\n}\nfunction advance(dt) {\n  const nextFrame = prevFrame;\n  for (let i = 0; i < currentFrame.length; i++) {\n    const animation = currentFrame[i];\n    priority = animation.priority;\n    if (!animation.idle) {\n      willAdvance(animation);\n      animation.advance(dt);\n      if (!animation.idle) {\n        nextFrame.push(animation);\n      }\n    }\n  }\n  priority = 0;\n  prevFrame = currentFrame;\n  prevFrame.length = 0;\n  currentFrame = nextFrame;\n  return currentFrame.length > 0;\n}\nfunction findIndex(arr, test) {\n  const index = arr.findIndex(test);\n  return index < 0 ? arr.length : index;\n}\n\n// src/clamp.ts\nvar clamp = (min, max, v) => Math.min(Math.max(v, min), max);\n\n// src/colors.ts\nvar colors2 = {\n  transparent: 0,\n  aliceblue: 4042850303,\n  antiquewhite: 4209760255,\n  aqua: 16777215,\n  aquamarine: 2147472639,\n  azure: 4043309055,\n  beige: 4126530815,\n  bisque: 4293182719,\n  black: 255,\n  blanchedalmond: 4293643775,\n  blue: 65535,\n  blueviolet: 2318131967,\n  brown: 2771004159,\n  burlywood: 3736635391,\n  burntsienna: 3934150143,\n  cadetblue: 1604231423,\n  chartreuse: 2147418367,\n  chocolate: 3530104575,\n  coral: 4286533887,\n  cornflowerblue: 1687547391,\n  cornsilk: 4294499583,\n  crimson: 3692313855,\n  cyan: 16777215,\n  darkblue: 35839,\n  darkcyan: 9145343,\n  darkgoldenrod: 3095792639,\n  darkgray: 2846468607,\n  darkgreen: 6553855,\n  darkgrey: 2846468607,\n  darkkhaki: 3182914559,\n  darkmagenta: 2332068863,\n  darkolivegreen: 1433087999,\n  darkorange: 4287365375,\n  darkorchid: 2570243327,\n  darkred: 2332033279,\n  darksalmon: 3918953215,\n  darkseagreen: 2411499519,\n  darkslateblue: 1211993087,\n  darkslategray: 793726975,\n  darkslategrey: 793726975,\n  darkturquoise: 13554175,\n  darkviolet: 2483082239,\n  deeppink: 4279538687,\n  deepskyblue: 12582911,\n  dimgray: 1768516095,\n  dimgrey: 1768516095,\n  dodgerblue: 512819199,\n  firebrick: 2988581631,\n  floralwhite: 4294635775,\n  forestgreen: 579543807,\n  fuchsia: 4278255615,\n  gainsboro: 3705462015,\n  ghostwhite: 4177068031,\n  gold: 4292280575,\n  goldenrod: 3668254975,\n  gray: 2155905279,\n  green: 8388863,\n  greenyellow: 2919182335,\n  grey: 2155905279,\n  honeydew: 4043305215,\n  hotpink: 4285117695,\n  indianred: 3445382399,\n  indigo: 1258324735,\n  ivory: 4294963455,\n  khaki: 4041641215,\n  lavender: 3873897215,\n  lavenderblush: 4293981695,\n  lawngreen: 2096890111,\n  lemonchiffon: 4294626815,\n  lightblue: 2916673279,\n  lightcoral: 4034953471,\n  lightcyan: 3774873599,\n  lightgoldenrodyellow: 4210742015,\n  lightgray: 3553874943,\n  lightgreen: 2431553791,\n  lightgrey: 3553874943,\n  lightpink: 4290167295,\n  lightsalmon: 4288707327,\n  lightseagreen: 548580095,\n  lightskyblue: 2278488831,\n  lightslategray: 2005441023,\n  lightslategrey: 2005441023,\n  lightsteelblue: 2965692159,\n  lightyellow: 4294959359,\n  lime: 16711935,\n  limegreen: 852308735,\n  linen: 4210091775,\n  magenta: 4278255615,\n  maroon: 2147483903,\n  mediumaquamarine: 1724754687,\n  mediumblue: 52735,\n  mediumorchid: 3126187007,\n  mediumpurple: 2473647103,\n  mediumseagreen: 1018393087,\n  mediumslateblue: 2070474495,\n  mediumspringgreen: 16423679,\n  mediumturquoise: 1221709055,\n  mediumvioletred: 3340076543,\n  midnightblue: 421097727,\n  mintcream: 4127193855,\n  mistyrose: 4293190143,\n  moccasin: 4293178879,\n  navajowhite: 4292783615,\n  navy: 33023,\n  oldlace: 4260751103,\n  olive: 2155872511,\n  olivedrab: 1804477439,\n  orange: 4289003775,\n  orangered: 4282712319,\n  orchid: 3664828159,\n  palegoldenrod: 4008225535,\n  palegreen: 2566625535,\n  paleturquoise: 2951671551,\n  palevioletred: 3681588223,\n  papayawhip: 4293907967,\n  peachpuff: 4292524543,\n  peru: 3448061951,\n  pink: 4290825215,\n  plum: 3718307327,\n  powderblue: 2967529215,\n  purple: 2147516671,\n  rebeccapurple: 1714657791,\n  red: 4278190335,\n  rosybrown: 3163525119,\n  royalblue: 1097458175,\n  saddlebrown: 2336560127,\n  salmon: 4202722047,\n  sandybrown: 4104413439,\n  seagreen: 780883967,\n  seashell: 4294307583,\n  sienna: 2689740287,\n  silver: 3233857791,\n  skyblue: 2278484991,\n  slateblue: 1784335871,\n  slategray: 1887473919,\n  slategrey: 1887473919,\n  snow: 4294638335,\n  springgreen: 16744447,\n  steelblue: 1182971135,\n  tan: 3535047935,\n  teal: 8421631,\n  thistle: 3636451583,\n  tomato: 4284696575,\n  turquoise: 1088475391,\n  violet: 4001558271,\n  wheat: 4125012991,\n  white: 4294967295,\n  whitesmoke: 4126537215,\n  yellow: 4294902015,\n  yellowgreen: 2597139199\n};\n\n// src/colorMatchers.ts\nvar NUMBER = \"[-+]?\\\\d*\\\\.?\\\\d+\";\nvar PERCENTAGE = NUMBER + \"%\";\nfunction call(...parts) {\n  return \"\\\\(\\\\s*(\" + parts.join(\")\\\\s*,\\\\s*(\") + \")\\\\s*\\\\)\";\n}\nvar rgb = new RegExp(\"rgb\" + call(NUMBER, NUMBER, NUMBER));\nvar rgba = new RegExp(\"rgba\" + call(NUMBER, NUMBER, NUMBER, NUMBER));\nvar hsl = new RegExp(\"hsl\" + call(NUMBER, PERCENTAGE, PERCENTAGE));\nvar hsla = new RegExp(\n  \"hsla\" + call(NUMBER, PERCENTAGE, PERCENTAGE, NUMBER)\n);\nvar hex3 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;\nvar hex4 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;\nvar hex6 = /^#([0-9a-fA-F]{6})$/;\nvar hex8 = /^#([0-9a-fA-F]{8})$/;\n\n// src/normalizeColor.ts\nfunction normalizeColor(color) {\n  let match;\n  if (typeof color === \"number\") {\n    return color >>> 0 === color && color >= 0 && color <= 4294967295 ? color : null;\n  }\n  if (match = hex6.exec(color))\n    return parseInt(match[1] + \"ff\", 16) >>> 0;\n  if (colors && colors[color] !== void 0) {\n    return colors[color];\n  }\n  if (match = rgb.exec(color)) {\n    return (parse255(match[1]) << 24 | // r\n    parse255(match[2]) << 16 | // g\n    parse255(match[3]) << 8 | // b\n    255) >>> // a\n    0;\n  }\n  if (match = rgba.exec(color)) {\n    return (parse255(match[1]) << 24 | // r\n    parse255(match[2]) << 16 | // g\n    parse255(match[3]) << 8 | // b\n    parse1(match[4])) >>> // a\n    0;\n  }\n  if (match = hex3.exec(color)) {\n    return parseInt(\n      match[1] + match[1] + // r\n      match[2] + match[2] + // g\n      match[3] + match[3] + // b\n      \"ff\",\n      // a\n      16\n    ) >>> 0;\n  }\n  if (match = hex8.exec(color))\n    return parseInt(match[1], 16) >>> 0;\n  if (match = hex4.exec(color)) {\n    return parseInt(\n      match[1] + match[1] + // r\n      match[2] + match[2] + // g\n      match[3] + match[3] + // b\n      match[4] + match[4],\n      // a\n      16\n    ) >>> 0;\n  }\n  if (match = hsl.exec(color)) {\n    return (hslToRgb(\n      parse360(match[1]),\n      // h\n      parsePercentage(match[2]),\n      // s\n      parsePercentage(match[3])\n      // l\n    ) | 255) >>> // a\n    0;\n  }\n  if (match = hsla.exec(color)) {\n    return (hslToRgb(\n      parse360(match[1]),\n      // h\n      parsePercentage(match[2]),\n      // s\n      parsePercentage(match[3])\n      // l\n    ) | parse1(match[4])) >>> // a\n    0;\n  }\n  return null;\n}\nfunction hue2rgb(p, q, t) {\n  if (t < 0)\n    t += 1;\n  if (t > 1)\n    t -= 1;\n  if (t < 1 / 6)\n    return p + (q - p) * 6 * t;\n  if (t < 1 / 2)\n    return q;\n  if (t < 2 / 3)\n    return p + (q - p) * (2 / 3 - t) * 6;\n  return p;\n}\nfunction hslToRgb(h, s, l) {\n  const q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n  const p = 2 * l - q;\n  const r = hue2rgb(p, q, h + 1 / 3);\n  const g = hue2rgb(p, q, h);\n  const b = hue2rgb(p, q, h - 1 / 3);\n  return Math.round(r * 255) << 24 | Math.round(g * 255) << 16 | Math.round(b * 255) << 8;\n}\nfunction parse255(str) {\n  const int = parseInt(str, 10);\n  if (int < 0)\n    return 0;\n  if (int > 255)\n    return 255;\n  return int;\n}\nfunction parse360(str) {\n  const int = parseFloat(str);\n  return (int % 360 + 360) % 360 / 360;\n}\nfunction parse1(str) {\n  const num = parseFloat(str);\n  if (num < 0)\n    return 0;\n  if (num > 1)\n    return 255;\n  return Math.round(num * 255);\n}\nfunction parsePercentage(str) {\n  const int = parseFloat(str);\n  if (int < 0)\n    return 0;\n  if (int > 100)\n    return 1;\n  return int / 100;\n}\n\n// src/colorToRgba.ts\nfunction colorToRgba(input) {\n  let int32Color = normalizeColor(input);\n  if (int32Color === null)\n    return input;\n  int32Color = int32Color || 0;\n  const r = (int32Color & 4278190080) >>> 24;\n  const g = (int32Color & 16711680) >>> 16;\n  const b = (int32Color & 65280) >>> 8;\n  const a = (int32Color & 255) / 255;\n  return `rgba(${r}, ${g}, ${b}, ${a})`;\n}\n\n// src/createInterpolator.ts\nvar createInterpolator = (range, output, extrapolate) => {\n  if (is.fun(range)) {\n    return range;\n  }\n  if (is.arr(range)) {\n    return createInterpolator({\n      range,\n      output,\n      extrapolate\n    });\n  }\n  if (is.str(range.output[0])) {\n    return createStringInterpolator(range);\n  }\n  const config = range;\n  const outputRange = config.output;\n  const inputRange = config.range || [0, 1];\n  const extrapolateLeft = config.extrapolateLeft || config.extrapolate || \"extend\";\n  const extrapolateRight = config.extrapolateRight || config.extrapolate || \"extend\";\n  const easing = config.easing || ((t) => t);\n  return (input) => {\n    const range2 = findRange(input, inputRange);\n    return interpolate(\n      input,\n      inputRange[range2],\n      inputRange[range2 + 1],\n      outputRange[range2],\n      outputRange[range2 + 1],\n      easing,\n      extrapolateLeft,\n      extrapolateRight,\n      config.map\n    );\n  };\n};\nfunction interpolate(input, inputMin, inputMax, outputMin, outputMax, easing, extrapolateLeft, extrapolateRight, map) {\n  let result = map ? map(input) : input;\n  if (result < inputMin) {\n    if (extrapolateLeft === \"identity\")\n      return result;\n    else if (extrapolateLeft === \"clamp\")\n      result = inputMin;\n  }\n  if (result > inputMax) {\n    if (extrapolateRight === \"identity\")\n      return result;\n    else if (extrapolateRight === \"clamp\")\n      result = inputMax;\n  }\n  if (outputMin === outputMax)\n    return outputMin;\n  if (inputMin === inputMax)\n    return input <= inputMin ? outputMin : outputMax;\n  if (inputMin === -Infinity)\n    result = -result;\n  else if (inputMax === Infinity)\n    result = result - inputMin;\n  else\n    result = (result - inputMin) / (inputMax - inputMin);\n  result = easing(result);\n  if (outputMin === -Infinity)\n    result = -result;\n  else if (outputMax === Infinity)\n    result = result + outputMin;\n  else\n    result = result * (outputMax - outputMin) + outputMin;\n  return result;\n}\nfunction findRange(input, inputRange) {\n  for (var i = 1; i < inputRange.length - 1; ++i)\n    if (inputRange[i] >= input)\n      break;\n  return i - 1;\n}\n\n// src/easings.ts\nvar steps = (steps2, direction = \"end\") => (progress2) => {\n  progress2 = direction === \"end\" ? Math.min(progress2, 0.999) : Math.max(progress2, 1e-3);\n  const expanded = progress2 * steps2;\n  const rounded = direction === \"end\" ? Math.floor(expanded) : Math.ceil(expanded);\n  return clamp(0, 1, rounded / steps2);\n};\nvar c1 = 1.70158;\nvar c2 = c1 * 1.525;\nvar c3 = c1 + 1;\nvar c4 = 2 * Math.PI / 3;\nvar c5 = 2 * Math.PI / 4.5;\nvar bounceOut = (x) => {\n  const n1 = 7.5625;\n  const d1 = 2.75;\n  if (x < 1 / d1) {\n    return n1 * x * x;\n  } else if (x < 2 / d1) {\n    return n1 * (x -= 1.5 / d1) * x + 0.75;\n  } else if (x < 2.5 / d1) {\n    return n1 * (x -= 2.25 / d1) * x + 0.9375;\n  } else {\n    return n1 * (x -= 2.625 / d1) * x + 0.984375;\n  }\n};\nvar easings = {\n  linear: (x) => x,\n  easeInQuad: (x) => x * x,\n  easeOutQuad: (x) => 1 - (1 - x) * (1 - x),\n  easeInOutQuad: (x) => x < 0.5 ? 2 * x * x : 1 - Math.pow(-2 * x + 2, 2) / 2,\n  easeInCubic: (x) => x * x * x,\n  easeOutCubic: (x) => 1 - Math.pow(1 - x, 3),\n  easeInOutCubic: (x) => x < 0.5 ? 4 * x * x * x : 1 - Math.pow(-2 * x + 2, 3) / 2,\n  easeInQuart: (x) => x * x * x * x,\n  easeOutQuart: (x) => 1 - Math.pow(1 - x, 4),\n  easeInOutQuart: (x) => x < 0.5 ? 8 * x * x * x * x : 1 - Math.pow(-2 * x + 2, 4) / 2,\n  easeInQuint: (x) => x * x * x * x * x,\n  easeOutQuint: (x) => 1 - Math.pow(1 - x, 5),\n  easeInOutQuint: (x) => x < 0.5 ? 16 * x * x * x * x * x : 1 - Math.pow(-2 * x + 2, 5) / 2,\n  easeInSine: (x) => 1 - Math.cos(x * Math.PI / 2),\n  easeOutSine: (x) => Math.sin(x * Math.PI / 2),\n  easeInOutSine: (x) => -(Math.cos(Math.PI * x) - 1) / 2,\n  easeInExpo: (x) => x === 0 ? 0 : Math.pow(2, 10 * x - 10),\n  easeOutExpo: (x) => x === 1 ? 1 : 1 - Math.pow(2, -10 * x),\n  easeInOutExpo: (x) => x === 0 ? 0 : x === 1 ? 1 : x < 0.5 ? Math.pow(2, 20 * x - 10) / 2 : (2 - Math.pow(2, -20 * x + 10)) / 2,\n  easeInCirc: (x) => 1 - Math.sqrt(1 - Math.pow(x, 2)),\n  easeOutCirc: (x) => Math.sqrt(1 - Math.pow(x - 1, 2)),\n  easeInOutCirc: (x) => x < 0.5 ? (1 - Math.sqrt(1 - Math.pow(2 * x, 2))) / 2 : (Math.sqrt(1 - Math.pow(-2 * x + 2, 2)) + 1) / 2,\n  easeInBack: (x) => c3 * x * x * x - c1 * x * x,\n  easeOutBack: (x) => 1 + c3 * Math.pow(x - 1, 3) + c1 * Math.pow(x - 1, 2),\n  easeInOutBack: (x) => x < 0.5 ? Math.pow(2 * x, 2) * ((c2 + 1) * 2 * x - c2) / 2 : (Math.pow(2 * x - 2, 2) * ((c2 + 1) * (x * 2 - 2) + c2) + 2) / 2,\n  easeInElastic: (x) => x === 0 ? 0 : x === 1 ? 1 : -Math.pow(2, 10 * x - 10) * Math.sin((x * 10 - 10.75) * c4),\n  easeOutElastic: (x) => x === 0 ? 0 : x === 1 ? 1 : Math.pow(2, -10 * x) * Math.sin((x * 10 - 0.75) * c4) + 1,\n  easeInOutElastic: (x) => x === 0 ? 0 : x === 1 ? 1 : x < 0.5 ? -(Math.pow(2, 20 * x - 10) * Math.sin((20 * x - 11.125) * c5)) / 2 : Math.pow(2, -20 * x + 10) * Math.sin((20 * x - 11.125) * c5) / 2 + 1,\n  easeInBounce: (x) => 1 - bounceOut(1 - x),\n  easeOutBounce: bounceOut,\n  easeInOutBounce: (x) => x < 0.5 ? (1 - bounceOut(1 - 2 * x)) / 2 : (1 + bounceOut(2 * x - 1)) / 2,\n  steps\n};\n\n// src/fluids.ts\nvar $get = Symbol.for(\"FluidValue.get\");\nvar $observers = Symbol.for(\"FluidValue.observers\");\nvar hasFluidValue = (arg) => Boolean(arg && arg[$get]);\nvar getFluidValue = (arg) => arg && arg[$get] ? arg[$get]() : arg;\nvar getFluidObservers = (target) => target[$observers] || null;\nfunction callFluidObserver(observer2, event) {\n  if (observer2.eventObserved) {\n    observer2.eventObserved(event);\n  } else {\n    observer2(event);\n  }\n}\nfunction callFluidObservers(target, event) {\n  const observers = target[$observers];\n  if (observers) {\n    observers.forEach((observer2) => {\n      callFluidObserver(observer2, event);\n    });\n  }\n}\nvar FluidValue = class {\n  constructor(get) {\n    if (!get && !(get = this.get)) {\n      throw Error(\"Unknown getter\");\n    }\n    setFluidGetter(this, get);\n  }\n};\n$get, $observers;\nvar setFluidGetter = (target, get) => setHidden(target, $get, get);\nfunction addFluidObserver(target, observer2) {\n  if (target[$get]) {\n    let observers = target[$observers];\n    if (!observers) {\n      setHidden(target, $observers, observers = /* @__PURE__ */ new Set());\n    }\n    if (!observers.has(observer2)) {\n      observers.add(observer2);\n      if (target.observerAdded) {\n        target.observerAdded(observers.size, observer2);\n      }\n    }\n  }\n  return observer2;\n}\nfunction removeFluidObserver(target, observer2) {\n  const observers = target[$observers];\n  if (observers && observers.has(observer2)) {\n    const count = observers.size - 1;\n    if (count) {\n      observers.delete(observer2);\n    } else {\n      target[$observers] = null;\n    }\n    if (target.observerRemoved) {\n      target.observerRemoved(count, observer2);\n    }\n  }\n}\nvar setHidden = (target, key, value) => Object.defineProperty(target, key, {\n  value,\n  writable: true,\n  configurable: true\n});\n\n// src/regexs.ts\nvar numberRegex = /[+\\-]?(?:0|[1-9]\\d*)(?:\\.\\d*)?(?:[eE][+\\-]?\\d+)?/g;\nvar colorRegex = /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\\((-?\\d+%?[,\\s]+){2,3}\\s*[\\d\\.]+%?\\))/gi;\nvar unitRegex = new RegExp(`(${numberRegex.source})(%|[a-z]+)`, \"i\");\nvar rgbaRegex = /rgba\\(([0-9\\.-]+), ([0-9\\.-]+), ([0-9\\.-]+), ([0-9\\.-]+)\\)/gi;\nvar cssVariableRegex = /var\\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\\)/;\n\n// src/variableToRgba.ts\nvar variableToRgba = (input) => {\n  const [token, fallback] = parseCSSVariable(input);\n  if (!token || isSSR()) {\n    return input;\n  }\n  const value = window.getComputedStyle(document.documentElement).getPropertyValue(token);\n  if (value) {\n    return value.trim();\n  } else if (fallback && fallback.startsWith(\"--\")) {\n    const value2 = window.getComputedStyle(document.documentElement).getPropertyValue(fallback);\n    if (value2) {\n      return value2;\n    } else {\n      return input;\n    }\n  } else if (fallback && cssVariableRegex.test(fallback)) {\n    return variableToRgba(fallback);\n  } else if (fallback) {\n    return fallback;\n  }\n  return input;\n};\nvar parseCSSVariable = (current) => {\n  const match = cssVariableRegex.exec(current);\n  if (!match)\n    return [,];\n  const [, token, fallback] = match;\n  return [token, fallback];\n};\n\n// src/stringInterpolation.ts\nvar namedColorRegex;\nvar rgbaRound = (_, p1, p2, p3, p4) => `rgba(${Math.round(p1)}, ${Math.round(p2)}, ${Math.round(p3)}, ${p4})`;\nvar createStringInterpolator2 = (config) => {\n  if (!namedColorRegex)\n    namedColorRegex = colors ? (\n      // match color names, ignore partial matches\n      new RegExp(`(${Object.keys(colors).join(\"|\")})(?!\\\\w)`, \"g\")\n    ) : (\n      // never match\n      /^\\b$/\n    );\n  const output = config.output.map((value) => {\n    return getFluidValue(value).replace(cssVariableRegex, variableToRgba).replace(colorRegex, colorToRgba).replace(namedColorRegex, colorToRgba);\n  });\n  const keyframes = output.map((value) => value.match(numberRegex).map(Number));\n  const outputRanges = keyframes[0].map(\n    (_, i) => keyframes.map((values) => {\n      if (!(i in values)) {\n        throw Error('The arity of each \"output\" value must be equal');\n      }\n      return values[i];\n    })\n  );\n  const interpolators = outputRanges.map(\n    (output2) => createInterpolator({ ...config, output: output2 })\n  );\n  return (input) => {\n    const missingUnit = !unitRegex.test(output[0]) && output.find((value) => unitRegex.test(value))?.replace(numberRegex, \"\");\n    let i = 0;\n    return output[0].replace(\n      numberRegex,\n      () => `${interpolators[i++](input)}${missingUnit || \"\"}`\n    ).replace(rgbaRegex, rgbaRound);\n  };\n};\n\n// src/deprecations.ts\nvar prefix = \"react-spring: \";\nvar once = (fn) => {\n  const func = fn;\n  let called = false;\n  if (typeof func != \"function\") {\n    throw new TypeError(`${prefix}once requires a function parameter`);\n  }\n  return (...args) => {\n    if (!called) {\n      func(...args);\n      called = true;\n    }\n  };\n};\nvar warnInterpolate = once(console.warn);\nfunction deprecateInterpolate() {\n  warnInterpolate(\n    `${prefix}The \"interpolate\" function is deprecated in v9 (use \"to\" instead)`\n  );\n}\nvar warnDirectCall = once(console.warn);\nfunction deprecateDirectCall() {\n  warnDirectCall(\n    `${prefix}Directly calling start instead of using the api object is deprecated in v9 (use \".start\" instead), this will be removed in later 0.X.0 versions`\n  );\n}\n\n// src/isAnimatedString.ts\nfunction isAnimatedString(value) {\n  return is.str(value) && (value[0] == \"#\" || /\\d/.test(value) || // Do not identify a CSS variable as an AnimatedString if its SSR\n  !isSSR() && cssVariableRegex.test(value) || value in (colors || {}));\n}\n\n// src/dom-events/scroll/index.ts\n\n\n// src/dom-events/resize/resizeElement.ts\nvar observer;\nvar resizeHandlers = /* @__PURE__ */ new WeakMap();\nvar handleObservation = (entries) => entries.forEach(({ target, contentRect }) => {\n  return resizeHandlers.get(target)?.forEach((handler) => handler(contentRect));\n});\nfunction resizeElement(handler, target) {\n  if (!observer) {\n    if (typeof ResizeObserver !== \"undefined\") {\n      observer = new ResizeObserver(handleObservation);\n    }\n  }\n  let elementHandlers = resizeHandlers.get(target);\n  if (!elementHandlers) {\n    elementHandlers = /* @__PURE__ */ new Set();\n    resizeHandlers.set(target, elementHandlers);\n  }\n  elementHandlers.add(handler);\n  if (observer) {\n    observer.observe(target);\n  }\n  return () => {\n    const elementHandlers2 = resizeHandlers.get(target);\n    if (!elementHandlers2)\n      return;\n    elementHandlers2.delete(handler);\n    if (!elementHandlers2.size && observer) {\n      observer.unobserve(target);\n    }\n  };\n}\n\n// src/dom-events/resize/resizeWindow.ts\nvar listeners = /* @__PURE__ */ new Set();\nvar cleanupWindowResizeHandler;\nvar createResizeHandler = () => {\n  const handleResize = () => {\n    listeners.forEach(\n      (callback) => callback({\n        width: window.innerWidth,\n        height: window.innerHeight\n      })\n    );\n  };\n  window.addEventListener(\"resize\", handleResize);\n  return () => {\n    window.removeEventListener(\"resize\", handleResize);\n  };\n};\nvar resizeWindow = (callback) => {\n  listeners.add(callback);\n  if (!cleanupWindowResizeHandler) {\n    cleanupWindowResizeHandler = createResizeHandler();\n  }\n  return () => {\n    listeners.delete(callback);\n    if (!listeners.size && cleanupWindowResizeHandler) {\n      cleanupWindowResizeHandler();\n      cleanupWindowResizeHandler = void 0;\n    }\n  };\n};\n\n// src/dom-events/resize/index.ts\nvar onResize = (callback, { container = document.documentElement } = {}) => {\n  if (container === document.documentElement) {\n    return resizeWindow(callback);\n  } else {\n    return resizeElement(callback, container);\n  }\n};\n\n// src/progress.ts\nvar progress = (min, max, value) => max - min === 0 ? 1 : (value - min) / (max - min);\n\n// src/dom-events/scroll/ScrollHandler.ts\nvar SCROLL_KEYS = {\n  x: {\n    length: \"Width\",\n    position: \"Left\"\n  },\n  y: {\n    length: \"Height\",\n    position: \"Top\"\n  }\n};\nvar ScrollHandler = class {\n  constructor(callback, container) {\n    this.createAxis = () => ({\n      current: 0,\n      progress: 0,\n      scrollLength: 0\n    });\n    this.updateAxis = (axisName) => {\n      const axis = this.info[axisName];\n      const { length, position } = SCROLL_KEYS[axisName];\n      axis.current = this.container[`scroll${position}`];\n      axis.scrollLength = this.container[`scroll${length}`] - this.container[`client${length}`];\n      axis.progress = progress(0, axis.scrollLength, axis.current);\n    };\n    this.update = () => {\n      this.updateAxis(\"x\");\n      this.updateAxis(\"y\");\n    };\n    this.sendEvent = () => {\n      this.callback(this.info);\n    };\n    this.advance = () => {\n      this.update();\n      this.sendEvent();\n    };\n    this.callback = callback;\n    this.container = container;\n    this.info = {\n      time: 0,\n      x: this.createAxis(),\n      y: this.createAxis()\n    };\n  }\n};\n\n// src/dom-events/scroll/index.ts\nvar scrollListeners = /* @__PURE__ */ new WeakMap();\nvar resizeListeners = /* @__PURE__ */ new WeakMap();\nvar onScrollHandlers = /* @__PURE__ */ new WeakMap();\nvar getTarget = (container) => container === document.documentElement ? window : container;\nvar onScroll = (callback, { container = document.documentElement } = {}) => {\n  let containerHandlers = onScrollHandlers.get(container);\n  if (!containerHandlers) {\n    containerHandlers = /* @__PURE__ */ new Set();\n    onScrollHandlers.set(container, containerHandlers);\n  }\n  const containerHandler = new ScrollHandler(callback, container);\n  containerHandlers.add(containerHandler);\n  if (!scrollListeners.has(container)) {\n    const listener = () => {\n      containerHandlers?.forEach((handler) => handler.advance());\n      return true;\n    };\n    scrollListeners.set(container, listener);\n    const target = getTarget(container);\n    window.addEventListener(\"resize\", listener, { passive: true });\n    if (container !== document.documentElement) {\n      resizeListeners.set(container, onResize(listener, { container }));\n    }\n    target.addEventListener(\"scroll\", listener, { passive: true });\n  }\n  const animateScroll = scrollListeners.get(container);\n  (0,_react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf)(animateScroll);\n  return () => {\n    _react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf.cancel(animateScroll);\n    const containerHandlers2 = onScrollHandlers.get(container);\n    if (!containerHandlers2)\n      return;\n    containerHandlers2.delete(containerHandler);\n    if (containerHandlers2.size)\n      return;\n    const listener = scrollListeners.get(container);\n    scrollListeners.delete(container);\n    if (listener) {\n      getTarget(container).removeEventListener(\"scroll\", listener);\n      window.removeEventListener(\"resize\", listener);\n      resizeListeners.get(container)?.();\n    }\n  };\n};\n\n// src/hooks/useConstant.ts\n\nfunction useConstant(init) {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n  if (ref.current === null) {\n    ref.current = init();\n  }\n  return ref.current;\n}\n\n// src/hooks/useForceUpdate.ts\n\n\n// src/hooks/useIsMounted.ts\n\n\n// src/hooks/useIsomorphicLayoutEffect.ts\n\nvar useIsomorphicLayoutEffect = isSSR() ? react__WEBPACK_IMPORTED_MODULE_1__.useEffect : react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect;\n\n// src/hooks/useIsMounted.ts\nvar useIsMounted = () => {\n  const isMounted = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n  useIsomorphicLayoutEffect(() => {\n    isMounted.current = true;\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n  return isMounted;\n};\n\n// src/hooks/useForceUpdate.ts\nfunction useForceUpdate() {\n  const update = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)()[1];\n  const isMounted = useIsMounted();\n  return () => {\n    if (isMounted.current) {\n      update(Math.random());\n    }\n  };\n}\n\n// src/hooks/useMemoOne.ts\n\nfunction useMemoOne(getResult, inputs) {\n  const [initial] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\n    () => ({\n      inputs,\n      result: getResult()\n    })\n  );\n  const committed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n  const prevCache = committed.current;\n  let cache = prevCache;\n  if (cache) {\n    const useCache = Boolean(\n      inputs && cache.inputs && areInputsEqual(inputs, cache.inputs)\n    );\n    if (!useCache) {\n      cache = {\n        inputs,\n        result: getResult()\n      };\n    }\n  } else {\n    cache = initial;\n  }\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    committed.current = cache;\n    if (prevCache == initial) {\n      initial.inputs = initial.result = void 0;\n    }\n  }, [cache]);\n  return cache.result;\n}\nfunction areInputsEqual(next, prev) {\n  if (next.length !== prev.length) {\n    return false;\n  }\n  for (let i = 0; i < next.length; i++) {\n    if (next[i] !== prev[i]) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// src/hooks/useOnce.ts\n\nvar useOnce = (effect) => (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(effect, emptyDeps);\nvar emptyDeps = [];\n\n// src/hooks/usePrev.ts\n\nfunction usePrev(value) {\n  const prevRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    prevRef.current = value;\n  });\n  return prevRef.current;\n}\n\n// src/hooks/useReducedMotion.ts\n\nvar useReducedMotion = () => {\n  const [reducedMotion, setReducedMotion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n  useIsomorphicLayoutEffect(() => {\n    const mql = window.matchMedia(\"(prefers-reduced-motion)\");\n    const handleMediaChange = (e) => {\n      setReducedMotion(e.matches);\n      assign({\n        skipAnimation: e.matches\n      });\n    };\n    handleMediaChange(mql);\n    if (mql.addEventListener) {\n      mql.addEventListener(\"change\", handleMediaChange);\n    } else {\n      mql.addListener(handleMediaChange);\n    }\n    return () => {\n      if (mql.removeEventListener) {\n        mql.removeEventListener(\"change\", handleMediaChange);\n      } else {\n        mql.removeListener(handleMediaChange);\n      }\n    };\n  }, []);\n  return reducedMotion;\n};\n\n// src/index.ts\n\n\n//# sourceMappingURL=react-spring_shared.modern.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlYWN0LXNwcmluZy9zaGFyZWQvZGlzdC9yZWFjdC1zcHJpbmdfc2hhcmVkLm1vZGVybi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixrQ0FBa0M7QUFDaEU7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUN3Qzs7QUFFekM7QUFDQTtBQUNBO0FBQ0EsMEVBQTBFLDJDQUEyQztBQUNySDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsY0FBYztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixnQkFBZ0I7QUFDcEMsOEJBQThCLEVBQUU7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLG1EQUFHO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLG1EQUFHO0FBQ1A7QUFDQSxJQUFJLG1EQUFHO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsSUFBSSxtREFBRztBQUNQOztBQUVBO0FBQ2lEO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTSxtREFBSTtBQUNWLE1BQU07QUFDTjtBQUNBLE1BQU0sdURBQUk7QUFDVjtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTSxtREFBSTtBQUNWLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFLHVEQUFJO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQix5QkFBeUI7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsRUFBRSxjQUFjLEVBQUUsY0FBYyxFQUFFO0FBQzdELDJCQUEyQixFQUFFLGNBQWMsRUFBRSxjQUFjLEVBQUUsY0FBYyxFQUFFO0FBQzdFLDJCQUEyQixFQUFFO0FBQzdCLDJCQUEyQixFQUFFOztBQUU3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRTtBQUNyQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLDJCQUEyQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBO0FBQ0EsZ0NBQWdDLEVBQUUsRUFBRSxJQUFJLFlBQVksRUFBRSwrQkFBK0IsSUFBSTtBQUN6RiwrQkFBK0IsbUJBQW1CO0FBQ2xEO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLCtDQUErQyxlQUFlLElBQUksZUFBZSxJQUFJLGVBQWUsSUFBSSxHQUFHO0FBQzNHO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLDhCQUE4QjtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLHNDQUFzQyw0QkFBNEI7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSwwQkFBMEIsRUFBRSxrQkFBa0I7QUFDN0Q7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixPQUFPO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPLE9BQU87QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTyxPQUFPO0FBQ2Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxvRUFBb0U7QUFDcEU7O0FBRUE7QUFDaUQ7O0FBRWpEO0FBQ0E7QUFDQTtBQUNBLHdEQUF3RCxxQkFBcUI7QUFDN0U7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSw0QkFBNEIsdUNBQXVDLElBQUk7QUFDdkU7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLGNBQWMsbUJBQW1CO0FBQ2pDLDZDQUE2QyxTQUFTO0FBQ3RELGtEQUFrRCxPQUFPLDZCQUE2QixPQUFPO0FBQzdGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsdUNBQXVDLElBQUk7QUFDdkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRCxlQUFlO0FBQ2pFO0FBQ0EsMERBQTBELFdBQVc7QUFDckU7QUFDQSxrREFBa0QsZUFBZTtBQUNqRTtBQUNBO0FBQ0EsRUFBRSx1REFBSTtBQUNOO0FBQ0EsSUFBSSxtREFBSTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUMrQjtBQUMvQjtBQUNBLGNBQWMsNkNBQU07QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNpQzs7QUFFakM7QUFDMEM7O0FBRTFDO0FBQ21EO0FBQ25ELDBDQUEwQyw0Q0FBUyxHQUFHLGtEQUFlOztBQUVyRTtBQUNBO0FBQ0Esb0JBQW9CLDZDQUFPO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsaUJBQWlCLCtDQUFRO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQzBGO0FBQzFGO0FBQ0Esb0JBQW9CLCtDQUFTO0FBQzdCO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLG9CQUFvQiw2Q0FBTztBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBLEVBQUUsZ0RBQVU7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsaUJBQWlCO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNnRDtBQUNoRCwwQkFBMEIsZ0RBQVU7QUFDcEM7O0FBRUE7QUFDbUU7QUFDbkU7QUFDQSxrQkFBa0IsNkNBQU87QUFDekIsRUFBRSxnREFBVTtBQUNaO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBRUE7QUFDOEM7QUFDOUM7QUFDQSw0Q0FBNEMsK0NBQVM7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBRUE7QUFDaUQ7QUFvRC9DO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZW50YWJlbGwvLi9ub2RlX21vZHVsZXMvQHJlYWN0LXNwcmluZy9zaGFyZWQvZGlzdC9yZWFjdC1zcHJpbmdfc2hhcmVkLm1vZGVybi5tanM/OGZlYSJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgX19kZWZQcm9wID0gT2JqZWN0LmRlZmluZVByb3BlcnR5O1xudmFyIF9fZXhwb3J0ID0gKHRhcmdldCwgYWxsKSA9PiB7XG4gIGZvciAodmFyIG5hbWUgaW4gYWxsKVxuICAgIF9fZGVmUHJvcCh0YXJnZXQsIG5hbWUsIHsgZ2V0OiBhbGxbbmFtZV0sIGVudW1lcmFibGU6IHRydWUgfSk7XG59O1xuXG4vLyBzcmMvZ2xvYmFscy50c1xudmFyIGdsb2JhbHNfZXhwb3J0cyA9IHt9O1xuX19leHBvcnQoZ2xvYmFsc19leHBvcnRzLCB7XG4gIGFzc2lnbjogKCkgPT4gYXNzaWduLFxuICBjb2xvcnM6ICgpID0+IGNvbG9ycyxcbiAgY3JlYXRlU3RyaW5nSW50ZXJwb2xhdG9yOiAoKSA9PiBjcmVhdGVTdHJpbmdJbnRlcnBvbGF0b3IsXG4gIHNraXBBbmltYXRpb246ICgpID0+IHNraXBBbmltYXRpb24sXG4gIHRvOiAoKSA9PiB0byxcbiAgd2lsbEFkdmFuY2U6ICgpID0+IHdpbGxBZHZhbmNlXG59KTtcbmltcG9ydCB7IHJhZiB9IGZyb20gXCJAcmVhY3Qtc3ByaW5nL3JhZnpcIjtcblxuLy8gc3JjL2hlbHBlcnMudHNcbmZ1bmN0aW9uIG5vb3AoKSB7XG59XG52YXIgZGVmaW5lSGlkZGVuID0gKG9iaiwga2V5LCB2YWx1ZSkgPT4gT2JqZWN0LmRlZmluZVByb3BlcnR5KG9iaiwga2V5LCB7IHZhbHVlLCB3cml0YWJsZTogdHJ1ZSwgY29uZmlndXJhYmxlOiB0cnVlIH0pO1xudmFyIGlzID0ge1xuICBhcnI6IEFycmF5LmlzQXJyYXksXG4gIG9iajogKGEpID0+ICEhYSAmJiBhLmNvbnN0cnVjdG9yLm5hbWUgPT09IFwiT2JqZWN0XCIsXG4gIGZ1bjogKGEpID0+IHR5cGVvZiBhID09PSBcImZ1bmN0aW9uXCIsXG4gIHN0cjogKGEpID0+IHR5cGVvZiBhID09PSBcInN0cmluZ1wiLFxuICBudW06IChhKSA9PiB0eXBlb2YgYSA9PT0gXCJudW1iZXJcIixcbiAgdW5kOiAoYSkgPT4gYSA9PT0gdm9pZCAwXG59O1xuZnVuY3Rpb24gaXNFcXVhbChhLCBiKSB7XG4gIGlmIChpcy5hcnIoYSkpIHtcbiAgICBpZiAoIWlzLmFycihiKSB8fCBhLmxlbmd0aCAhPT0gYi5sZW5ndGgpXG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBhLmxlbmd0aDsgaSsrKSB7XG4gICAgICBpZiAoYVtpXSAhPT0gYltpXSlcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbiAgfVxuICByZXR1cm4gYSA9PT0gYjtcbn1cbnZhciBlYWNoID0gKG9iaiwgZm4pID0+IG9iai5mb3JFYWNoKGZuKTtcbmZ1bmN0aW9uIGVhY2hQcm9wKG9iaiwgZm4sIGN0eCkge1xuICBpZiAoaXMuYXJyKG9iaikpIHtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IG9iai5sZW5ndGg7IGkrKykge1xuICAgICAgZm4uY2FsbChjdHgsIG9ialtpXSwgYCR7aX1gKTtcbiAgICB9XG4gICAgcmV0dXJuO1xuICB9XG4gIGZvciAoY29uc3Qga2V5IGluIG9iaikge1xuICAgIGlmIChvYmouaGFzT3duUHJvcGVydHkoa2V5KSkge1xuICAgICAgZm4uY2FsbChjdHgsIG9ialtrZXldLCBrZXkpO1xuICAgIH1cbiAgfVxufVxudmFyIHRvQXJyYXkgPSAoYSkgPT4gaXMudW5kKGEpID8gW10gOiBpcy5hcnIoYSkgPyBhIDogW2FdO1xuZnVuY3Rpb24gZmx1c2gocXVldWUsIGl0ZXJhdG9yKSB7XG4gIGlmIChxdWV1ZS5zaXplKSB7XG4gICAgY29uc3QgaXRlbXMgPSBBcnJheS5mcm9tKHF1ZXVlKTtcbiAgICBxdWV1ZS5jbGVhcigpO1xuICAgIGVhY2goaXRlbXMsIGl0ZXJhdG9yKTtcbiAgfVxufVxudmFyIGZsdXNoQ2FsbHMgPSAocXVldWUsIC4uLmFyZ3MpID0+IGZsdXNoKHF1ZXVlLCAoZm4pID0+IGZuKC4uLmFyZ3MpKTtcbnZhciBpc1NTUiA9ICgpID0+IHR5cGVvZiB3aW5kb3cgPT09IFwidW5kZWZpbmVkXCIgfHwgIXdpbmRvdy5uYXZpZ2F0b3IgfHwgL1NlcnZlclNpZGVSZW5kZXJpbmd8XkRlbm9cXC8vLnRlc3Qod2luZG93Lm5hdmlnYXRvci51c2VyQWdlbnQpO1xuXG4vLyBzcmMvZ2xvYmFscy50c1xudmFyIGNyZWF0ZVN0cmluZ0ludGVycG9sYXRvcjtcbnZhciB0bztcbnZhciBjb2xvcnMgPSBudWxsO1xudmFyIHNraXBBbmltYXRpb24gPSBmYWxzZTtcbnZhciB3aWxsQWR2YW5jZSA9IG5vb3A7XG52YXIgYXNzaWduID0gKGdsb2JhbHMpID0+IHtcbiAgaWYgKGdsb2JhbHMudG8pXG4gICAgdG8gPSBnbG9iYWxzLnRvO1xuICBpZiAoZ2xvYmFscy5ub3cpXG4gICAgcmFmLm5vdyA9IGdsb2JhbHMubm93O1xuICBpZiAoZ2xvYmFscy5jb2xvcnMgIT09IHZvaWQgMClcbiAgICBjb2xvcnMgPSBnbG9iYWxzLmNvbG9ycztcbiAgaWYgKGdsb2JhbHMuc2tpcEFuaW1hdGlvbiAhPSBudWxsKVxuICAgIHNraXBBbmltYXRpb24gPSBnbG9iYWxzLnNraXBBbmltYXRpb247XG4gIGlmIChnbG9iYWxzLmNyZWF0ZVN0cmluZ0ludGVycG9sYXRvcilcbiAgICBjcmVhdGVTdHJpbmdJbnRlcnBvbGF0b3IgPSBnbG9iYWxzLmNyZWF0ZVN0cmluZ0ludGVycG9sYXRvcjtcbiAgaWYgKGdsb2JhbHMucmVxdWVzdEFuaW1hdGlvbkZyYW1lKVxuICAgIHJhZi51c2UoZ2xvYmFscy5yZXF1ZXN0QW5pbWF0aW9uRnJhbWUpO1xuICBpZiAoZ2xvYmFscy5iYXRjaGVkVXBkYXRlcylcbiAgICByYWYuYmF0Y2hlZFVwZGF0ZXMgPSBnbG9iYWxzLmJhdGNoZWRVcGRhdGVzO1xuICBpZiAoZ2xvYmFscy53aWxsQWR2YW5jZSlcbiAgICB3aWxsQWR2YW5jZSA9IGdsb2JhbHMud2lsbEFkdmFuY2U7XG4gIGlmIChnbG9iYWxzLmZyYW1lTG9vcClcbiAgICByYWYuZnJhbWVMb29wID0gZ2xvYmFscy5mcmFtZUxvb3A7XG59O1xuXG4vLyBzcmMvRnJhbWVMb29wLnRzXG5pbXBvcnQgeyByYWYgYXMgcmFmMiB9IGZyb20gXCJAcmVhY3Qtc3ByaW5nL3JhZnpcIjtcbnZhciBzdGFydFF1ZXVlID0gLyogQF9fUFVSRV9fICovIG5ldyBTZXQoKTtcbnZhciBjdXJyZW50RnJhbWUgPSBbXTtcbnZhciBwcmV2RnJhbWUgPSBbXTtcbnZhciBwcmlvcml0eSA9IDA7XG52YXIgZnJhbWVMb29wID0ge1xuICBnZXQgaWRsZSgpIHtcbiAgICByZXR1cm4gIXN0YXJ0UXVldWUuc2l6ZSAmJiAhY3VycmVudEZyYW1lLmxlbmd0aDtcbiAgfSxcbiAgLyoqIEFkdmFuY2UgdGhlIGdpdmVuIGFuaW1hdGlvbiBvbiBldmVyeSBmcmFtZSB1bnRpbCBpZGxlLiAqL1xuICBzdGFydChhbmltYXRpb24pIHtcbiAgICBpZiAocHJpb3JpdHkgPiBhbmltYXRpb24ucHJpb3JpdHkpIHtcbiAgICAgIHN0YXJ0UXVldWUuYWRkKGFuaW1hdGlvbik7XG4gICAgICByYWYyLm9uU3RhcnQoZmx1c2hTdGFydFF1ZXVlKTtcbiAgICB9IGVsc2Uge1xuICAgICAgc3RhcnRTYWZlbHkoYW5pbWF0aW9uKTtcbiAgICAgIHJhZjIoYWR2YW5jZSk7XG4gICAgfVxuICB9LFxuICAvKiogQWR2YW5jZSBhbGwgYW5pbWF0aW9ucyBieSB0aGUgZ2l2ZW4gdGltZS4gKi9cbiAgYWR2YW5jZSxcbiAgLyoqIENhbGwgdGhpcyB3aGVuIGFuIGFuaW1hdGlvbidzIHByaW9yaXR5IGNoYW5nZXMuICovXG4gIHNvcnQoYW5pbWF0aW9uKSB7XG4gICAgaWYgKHByaW9yaXR5KSB7XG4gICAgICByYWYyLm9uRnJhbWUoKCkgPT4gZnJhbWVMb29wLnNvcnQoYW5pbWF0aW9uKSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnN0IHByZXZJbmRleCA9IGN1cnJlbnRGcmFtZS5pbmRleE9mKGFuaW1hdGlvbik7XG4gICAgICBpZiAofnByZXZJbmRleCkge1xuICAgICAgICBjdXJyZW50RnJhbWUuc3BsaWNlKHByZXZJbmRleCwgMSk7XG4gICAgICAgIHN0YXJ0VW5zYWZlbHkoYW5pbWF0aW9uKTtcbiAgICAgIH1cbiAgICB9XG4gIH0sXG4gIC8qKlxuICAgKiBDbGVhciBhbGwgYW5pbWF0aW9ucy4gRm9yIHRlc3RpbmcgcHVycG9zZXMuXG4gICAqXG4gICAqIOKYoO+4jyBOZXZlciBjYWxsIHRoaXMgZnJvbSB3aXRoaW4gdGhlIGZyYW1lbG9vcC5cbiAgICovXG4gIGNsZWFyKCkge1xuICAgIGN1cnJlbnRGcmFtZSA9IFtdO1xuICAgIHN0YXJ0UXVldWUuY2xlYXIoKTtcbiAgfVxufTtcbmZ1bmN0aW9uIGZsdXNoU3RhcnRRdWV1ZSgpIHtcbiAgc3RhcnRRdWV1ZS5mb3JFYWNoKHN0YXJ0U2FmZWx5KTtcbiAgc3RhcnRRdWV1ZS5jbGVhcigpO1xuICByYWYyKGFkdmFuY2UpO1xufVxuZnVuY3Rpb24gc3RhcnRTYWZlbHkoYW5pbWF0aW9uKSB7XG4gIGlmICghY3VycmVudEZyYW1lLmluY2x1ZGVzKGFuaW1hdGlvbikpXG4gICAgc3RhcnRVbnNhZmVseShhbmltYXRpb24pO1xufVxuZnVuY3Rpb24gc3RhcnRVbnNhZmVseShhbmltYXRpb24pIHtcbiAgY3VycmVudEZyYW1lLnNwbGljZShcbiAgICBmaW5kSW5kZXgoY3VycmVudEZyYW1lLCAob3RoZXIpID0+IG90aGVyLnByaW9yaXR5ID4gYW5pbWF0aW9uLnByaW9yaXR5KSxcbiAgICAwLFxuICAgIGFuaW1hdGlvblxuICApO1xufVxuZnVuY3Rpb24gYWR2YW5jZShkdCkge1xuICBjb25zdCBuZXh0RnJhbWUgPSBwcmV2RnJhbWU7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgY3VycmVudEZyYW1lLmxlbmd0aDsgaSsrKSB7XG4gICAgY29uc3QgYW5pbWF0aW9uID0gY3VycmVudEZyYW1lW2ldO1xuICAgIHByaW9yaXR5ID0gYW5pbWF0aW9uLnByaW9yaXR5O1xuICAgIGlmICghYW5pbWF0aW9uLmlkbGUpIHtcbiAgICAgIHdpbGxBZHZhbmNlKGFuaW1hdGlvbik7XG4gICAgICBhbmltYXRpb24uYWR2YW5jZShkdCk7XG4gICAgICBpZiAoIWFuaW1hdGlvbi5pZGxlKSB7XG4gICAgICAgIG5leHRGcmFtZS5wdXNoKGFuaW1hdGlvbik7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHByaW9yaXR5ID0gMDtcbiAgcHJldkZyYW1lID0gY3VycmVudEZyYW1lO1xuICBwcmV2RnJhbWUubGVuZ3RoID0gMDtcbiAgY3VycmVudEZyYW1lID0gbmV4dEZyYW1lO1xuICByZXR1cm4gY3VycmVudEZyYW1lLmxlbmd0aCA+IDA7XG59XG5mdW5jdGlvbiBmaW5kSW5kZXgoYXJyLCB0ZXN0KSB7XG4gIGNvbnN0IGluZGV4ID0gYXJyLmZpbmRJbmRleCh0ZXN0KTtcbiAgcmV0dXJuIGluZGV4IDwgMCA/IGFyci5sZW5ndGggOiBpbmRleDtcbn1cblxuLy8gc3JjL2NsYW1wLnRzXG52YXIgY2xhbXAgPSAobWluLCBtYXgsIHYpID0+IE1hdGgubWluKE1hdGgubWF4KHYsIG1pbiksIG1heCk7XG5cbi8vIHNyYy9jb2xvcnMudHNcbnZhciBjb2xvcnMyID0ge1xuICB0cmFuc3BhcmVudDogMCxcbiAgYWxpY2VibHVlOiA0MDQyODUwMzAzLFxuICBhbnRpcXVld2hpdGU6IDQyMDk3NjAyNTUsXG4gIGFxdWE6IDE2Nzc3MjE1LFxuICBhcXVhbWFyaW5lOiAyMTQ3NDcyNjM5LFxuICBhenVyZTogNDA0MzMwOTA1NSxcbiAgYmVpZ2U6IDQxMjY1MzA4MTUsXG4gIGJpc3F1ZTogNDI5MzE4MjcxOSxcbiAgYmxhY2s6IDI1NSxcbiAgYmxhbmNoZWRhbG1vbmQ6IDQyOTM2NDM3NzUsXG4gIGJsdWU6IDY1NTM1LFxuICBibHVldmlvbGV0OiAyMzE4MTMxOTY3LFxuICBicm93bjogMjc3MTAwNDE1OSxcbiAgYnVybHl3b29kOiAzNzM2NjM1MzkxLFxuICBidXJudHNpZW5uYTogMzkzNDE1MDE0MyxcbiAgY2FkZXRibHVlOiAxNjA0MjMxNDIzLFxuICBjaGFydHJldXNlOiAyMTQ3NDE4MzY3LFxuICBjaG9jb2xhdGU6IDM1MzAxMDQ1NzUsXG4gIGNvcmFsOiA0Mjg2NTMzODg3LFxuICBjb3JuZmxvd2VyYmx1ZTogMTY4NzU0NzM5MSxcbiAgY29ybnNpbGs6IDQyOTQ0OTk1ODMsXG4gIGNyaW1zb246IDM2OTIzMTM4NTUsXG4gIGN5YW46IDE2Nzc3MjE1LFxuICBkYXJrYmx1ZTogMzU4MzksXG4gIGRhcmtjeWFuOiA5MTQ1MzQzLFxuICBkYXJrZ29sZGVucm9kOiAzMDk1NzkyNjM5LFxuICBkYXJrZ3JheTogMjg0NjQ2ODYwNyxcbiAgZGFya2dyZWVuOiA2NTUzODU1LFxuICBkYXJrZ3JleTogMjg0NjQ2ODYwNyxcbiAgZGFya2toYWtpOiAzMTgyOTE0NTU5LFxuICBkYXJrbWFnZW50YTogMjMzMjA2ODg2MyxcbiAgZGFya29saXZlZ3JlZW46IDE0MzMwODc5OTksXG4gIGRhcmtvcmFuZ2U6IDQyODczNjUzNzUsXG4gIGRhcmtvcmNoaWQ6IDI1NzAyNDMzMjcsXG4gIGRhcmtyZWQ6IDIzMzIwMzMyNzksXG4gIGRhcmtzYWxtb246IDM5MTg5NTMyMTUsXG4gIGRhcmtzZWFncmVlbjogMjQxMTQ5OTUxOSxcbiAgZGFya3NsYXRlYmx1ZTogMTIxMTk5MzA4NyxcbiAgZGFya3NsYXRlZ3JheTogNzkzNzI2OTc1LFxuICBkYXJrc2xhdGVncmV5OiA3OTM3MjY5NzUsXG4gIGRhcmt0dXJxdW9pc2U6IDEzNTU0MTc1LFxuICBkYXJrdmlvbGV0OiAyNDgzMDgyMjM5LFxuICBkZWVwcGluazogNDI3OTUzODY4NyxcbiAgZGVlcHNreWJsdWU6IDEyNTgyOTExLFxuICBkaW1ncmF5OiAxNzY4NTE2MDk1LFxuICBkaW1ncmV5OiAxNzY4NTE2MDk1LFxuICBkb2RnZXJibHVlOiA1MTI4MTkxOTksXG4gIGZpcmVicmljazogMjk4ODU4MTYzMSxcbiAgZmxvcmFsd2hpdGU6IDQyOTQ2MzU3NzUsXG4gIGZvcmVzdGdyZWVuOiA1Nzk1NDM4MDcsXG4gIGZ1Y2hzaWE6IDQyNzgyNTU2MTUsXG4gIGdhaW5zYm9ybzogMzcwNTQ2MjAxNSxcbiAgZ2hvc3R3aGl0ZTogNDE3NzA2ODAzMSxcbiAgZ29sZDogNDI5MjI4MDU3NSxcbiAgZ29sZGVucm9kOiAzNjY4MjU0OTc1LFxuICBncmF5OiAyMTU1OTA1Mjc5LFxuICBncmVlbjogODM4ODg2MyxcbiAgZ3JlZW55ZWxsb3c6IDI5MTkxODIzMzUsXG4gIGdyZXk6IDIxNTU5MDUyNzksXG4gIGhvbmV5ZGV3OiA0MDQzMzA1MjE1LFxuICBob3RwaW5rOiA0Mjg1MTE3Njk1LFxuICBpbmRpYW5yZWQ6IDM0NDUzODIzOTksXG4gIGluZGlnbzogMTI1ODMyNDczNSxcbiAgaXZvcnk6IDQyOTQ5NjM0NTUsXG4gIGtoYWtpOiA0MDQxNjQxMjE1LFxuICBsYXZlbmRlcjogMzg3Mzg5NzIxNSxcbiAgbGF2ZW5kZXJibHVzaDogNDI5Mzk4MTY5NSxcbiAgbGF3bmdyZWVuOiAyMDk2ODkwMTExLFxuICBsZW1vbmNoaWZmb246IDQyOTQ2MjY4MTUsXG4gIGxpZ2h0Ymx1ZTogMjkxNjY3MzI3OSxcbiAgbGlnaHRjb3JhbDogNDAzNDk1MzQ3MSxcbiAgbGlnaHRjeWFuOiAzNzc0ODczNTk5LFxuICBsaWdodGdvbGRlbnJvZHllbGxvdzogNDIxMDc0MjAxNSxcbiAgbGlnaHRncmF5OiAzNTUzODc0OTQzLFxuICBsaWdodGdyZWVuOiAyNDMxNTUzNzkxLFxuICBsaWdodGdyZXk6IDM1NTM4NzQ5NDMsXG4gIGxpZ2h0cGluazogNDI5MDE2NzI5NSxcbiAgbGlnaHRzYWxtb246IDQyODg3MDczMjcsXG4gIGxpZ2h0c2VhZ3JlZW46IDU0ODU4MDA5NSxcbiAgbGlnaHRza3libHVlOiAyMjc4NDg4ODMxLFxuICBsaWdodHNsYXRlZ3JheTogMjAwNTQ0MTAyMyxcbiAgbGlnaHRzbGF0ZWdyZXk6IDIwMDU0NDEwMjMsXG4gIGxpZ2h0c3RlZWxibHVlOiAyOTY1NjkyMTU5LFxuICBsaWdodHllbGxvdzogNDI5NDk1OTM1OSxcbiAgbGltZTogMTY3MTE5MzUsXG4gIGxpbWVncmVlbjogODUyMzA4NzM1LFxuICBsaW5lbjogNDIxMDA5MTc3NSxcbiAgbWFnZW50YTogNDI3ODI1NTYxNSxcbiAgbWFyb29uOiAyMTQ3NDgzOTAzLFxuICBtZWRpdW1hcXVhbWFyaW5lOiAxNzI0NzU0Njg3LFxuICBtZWRpdW1ibHVlOiA1MjczNSxcbiAgbWVkaXVtb3JjaGlkOiAzMTI2MTg3MDA3LFxuICBtZWRpdW1wdXJwbGU6IDI0NzM2NDcxMDMsXG4gIG1lZGl1bXNlYWdyZWVuOiAxMDE4MzkzMDg3LFxuICBtZWRpdW1zbGF0ZWJsdWU6IDIwNzA0NzQ0OTUsXG4gIG1lZGl1bXNwcmluZ2dyZWVuOiAxNjQyMzY3OSxcbiAgbWVkaXVtdHVycXVvaXNlOiAxMjIxNzA5MDU1LFxuICBtZWRpdW12aW9sZXRyZWQ6IDMzNDAwNzY1NDMsXG4gIG1pZG5pZ2h0Ymx1ZTogNDIxMDk3NzI3LFxuICBtaW50Y3JlYW06IDQxMjcxOTM4NTUsXG4gIG1pc3R5cm9zZTogNDI5MzE5MDE0MyxcbiAgbW9jY2FzaW46IDQyOTMxNzg4NzksXG4gIG5hdmFqb3doaXRlOiA0MjkyNzgzNjE1LFxuICBuYXZ5OiAzMzAyMyxcbiAgb2xkbGFjZTogNDI2MDc1MTEwMyxcbiAgb2xpdmU6IDIxNTU4NzI1MTEsXG4gIG9saXZlZHJhYjogMTgwNDQ3NzQzOSxcbiAgb3JhbmdlOiA0Mjg5MDAzNzc1LFxuICBvcmFuZ2VyZWQ6IDQyODI3MTIzMTksXG4gIG9yY2hpZDogMzY2NDgyODE1OSxcbiAgcGFsZWdvbGRlbnJvZDogNDAwODIyNTUzNSxcbiAgcGFsZWdyZWVuOiAyNTY2NjI1NTM1LFxuICBwYWxldHVycXVvaXNlOiAyOTUxNjcxNTUxLFxuICBwYWxldmlvbGV0cmVkOiAzNjgxNTg4MjIzLFxuICBwYXBheWF3aGlwOiA0MjkzOTA3OTY3LFxuICBwZWFjaHB1ZmY6IDQyOTI1MjQ1NDMsXG4gIHBlcnU6IDM0NDgwNjE5NTEsXG4gIHBpbms6IDQyOTA4MjUyMTUsXG4gIHBsdW06IDM3MTgzMDczMjcsXG4gIHBvd2RlcmJsdWU6IDI5Njc1MjkyMTUsXG4gIHB1cnBsZTogMjE0NzUxNjY3MSxcbiAgcmViZWNjYXB1cnBsZTogMTcxNDY1Nzc5MSxcbiAgcmVkOiA0Mjc4MTkwMzM1LFxuICByb3N5YnJvd246IDMxNjM1MjUxMTksXG4gIHJveWFsYmx1ZTogMTA5NzQ1ODE3NSxcbiAgc2FkZGxlYnJvd246IDIzMzY1NjAxMjcsXG4gIHNhbG1vbjogNDIwMjcyMjA0NyxcbiAgc2FuZHlicm93bjogNDEwNDQxMzQzOSxcbiAgc2VhZ3JlZW46IDc4MDg4Mzk2NyxcbiAgc2Vhc2hlbGw6IDQyOTQzMDc1ODMsXG4gIHNpZW5uYTogMjY4OTc0MDI4NyxcbiAgc2lsdmVyOiAzMjMzODU3NzkxLFxuICBza3libHVlOiAyMjc4NDg0OTkxLFxuICBzbGF0ZWJsdWU6IDE3ODQzMzU4NzEsXG4gIHNsYXRlZ3JheTogMTg4NzQ3MzkxOSxcbiAgc2xhdGVncmV5OiAxODg3NDczOTE5LFxuICBzbm93OiA0Mjk0NjM4MzM1LFxuICBzcHJpbmdncmVlbjogMTY3NDQ0NDcsXG4gIHN0ZWVsYmx1ZTogMTE4Mjk3MTEzNSxcbiAgdGFuOiAzNTM1MDQ3OTM1LFxuICB0ZWFsOiA4NDIxNjMxLFxuICB0aGlzdGxlOiAzNjM2NDUxNTgzLFxuICB0b21hdG86IDQyODQ2OTY1NzUsXG4gIHR1cnF1b2lzZTogMTA4ODQ3NTM5MSxcbiAgdmlvbGV0OiA0MDAxNTU4MjcxLFxuICB3aGVhdDogNDEyNTAxMjk5MSxcbiAgd2hpdGU6IDQyOTQ5NjcyOTUsXG4gIHdoaXRlc21va2U6IDQxMjY1MzcyMTUsXG4gIHllbGxvdzogNDI5NDkwMjAxNSxcbiAgeWVsbG93Z3JlZW46IDI1OTcxMzkxOTlcbn07XG5cbi8vIHNyYy9jb2xvck1hdGNoZXJzLnRzXG52YXIgTlVNQkVSID0gXCJbLStdP1xcXFxkKlxcXFwuP1xcXFxkK1wiO1xudmFyIFBFUkNFTlRBR0UgPSBOVU1CRVIgKyBcIiVcIjtcbmZ1bmN0aW9uIGNhbGwoLi4ucGFydHMpIHtcbiAgcmV0dXJuIFwiXFxcXChcXFxccyooXCIgKyBwYXJ0cy5qb2luKFwiKVxcXFxzKixcXFxccyooXCIpICsgXCIpXFxcXHMqXFxcXClcIjtcbn1cbnZhciByZ2IgPSBuZXcgUmVnRXhwKFwicmdiXCIgKyBjYWxsKE5VTUJFUiwgTlVNQkVSLCBOVU1CRVIpKTtcbnZhciByZ2JhID0gbmV3IFJlZ0V4cChcInJnYmFcIiArIGNhbGwoTlVNQkVSLCBOVU1CRVIsIE5VTUJFUiwgTlVNQkVSKSk7XG52YXIgaHNsID0gbmV3IFJlZ0V4cChcImhzbFwiICsgY2FsbChOVU1CRVIsIFBFUkNFTlRBR0UsIFBFUkNFTlRBR0UpKTtcbnZhciBoc2xhID0gbmV3IFJlZ0V4cChcbiAgXCJoc2xhXCIgKyBjYWxsKE5VTUJFUiwgUEVSQ0VOVEFHRSwgUEVSQ0VOVEFHRSwgTlVNQkVSKVxuKTtcbnZhciBoZXgzID0gL14jKFswLTlhLWZBLUZdezF9KShbMC05YS1mQS1GXXsxfSkoWzAtOWEtZkEtRl17MX0pJC87XG52YXIgaGV4NCA9IC9eIyhbMC05YS1mQS1GXXsxfSkoWzAtOWEtZkEtRl17MX0pKFswLTlhLWZBLUZdezF9KShbMC05YS1mQS1GXXsxfSkkLztcbnZhciBoZXg2ID0gL14jKFswLTlhLWZBLUZdezZ9KSQvO1xudmFyIGhleDggPSAvXiMoWzAtOWEtZkEtRl17OH0pJC87XG5cbi8vIHNyYy9ub3JtYWxpemVDb2xvci50c1xuZnVuY3Rpb24gbm9ybWFsaXplQ29sb3IoY29sb3IpIHtcbiAgbGV0IG1hdGNoO1xuICBpZiAodHlwZW9mIGNvbG9yID09PSBcIm51bWJlclwiKSB7XG4gICAgcmV0dXJuIGNvbG9yID4+PiAwID09PSBjb2xvciAmJiBjb2xvciA+PSAwICYmIGNvbG9yIDw9IDQyOTQ5NjcyOTUgPyBjb2xvciA6IG51bGw7XG4gIH1cbiAgaWYgKG1hdGNoID0gaGV4Ni5leGVjKGNvbG9yKSlcbiAgICByZXR1cm4gcGFyc2VJbnQobWF0Y2hbMV0gKyBcImZmXCIsIDE2KSA+Pj4gMDtcbiAgaWYgKGNvbG9ycyAmJiBjb2xvcnNbY29sb3JdICE9PSB2b2lkIDApIHtcbiAgICByZXR1cm4gY29sb3JzW2NvbG9yXTtcbiAgfVxuICBpZiAobWF0Y2ggPSByZ2IuZXhlYyhjb2xvcikpIHtcbiAgICByZXR1cm4gKHBhcnNlMjU1KG1hdGNoWzFdKSA8PCAyNCB8IC8vIHJcbiAgICBwYXJzZTI1NShtYXRjaFsyXSkgPDwgMTYgfCAvLyBnXG4gICAgcGFyc2UyNTUobWF0Y2hbM10pIDw8IDggfCAvLyBiXG4gICAgMjU1KSA+Pj4gLy8gYVxuICAgIDA7XG4gIH1cbiAgaWYgKG1hdGNoID0gcmdiYS5leGVjKGNvbG9yKSkge1xuICAgIHJldHVybiAocGFyc2UyNTUobWF0Y2hbMV0pIDw8IDI0IHwgLy8gclxuICAgIHBhcnNlMjU1KG1hdGNoWzJdKSA8PCAxNiB8IC8vIGdcbiAgICBwYXJzZTI1NShtYXRjaFszXSkgPDwgOCB8IC8vIGJcbiAgICBwYXJzZTEobWF0Y2hbNF0pKSA+Pj4gLy8gYVxuICAgIDA7XG4gIH1cbiAgaWYgKG1hdGNoID0gaGV4My5leGVjKGNvbG9yKSkge1xuICAgIHJldHVybiBwYXJzZUludChcbiAgICAgIG1hdGNoWzFdICsgbWF0Y2hbMV0gKyAvLyByXG4gICAgICBtYXRjaFsyXSArIG1hdGNoWzJdICsgLy8gZ1xuICAgICAgbWF0Y2hbM10gKyBtYXRjaFszXSArIC8vIGJcbiAgICAgIFwiZmZcIixcbiAgICAgIC8vIGFcbiAgICAgIDE2XG4gICAgKSA+Pj4gMDtcbiAgfVxuICBpZiAobWF0Y2ggPSBoZXg4LmV4ZWMoY29sb3IpKVxuICAgIHJldHVybiBwYXJzZUludChtYXRjaFsxXSwgMTYpID4+PiAwO1xuICBpZiAobWF0Y2ggPSBoZXg0LmV4ZWMoY29sb3IpKSB7XG4gICAgcmV0dXJuIHBhcnNlSW50KFxuICAgICAgbWF0Y2hbMV0gKyBtYXRjaFsxXSArIC8vIHJcbiAgICAgIG1hdGNoWzJdICsgbWF0Y2hbMl0gKyAvLyBnXG4gICAgICBtYXRjaFszXSArIG1hdGNoWzNdICsgLy8gYlxuICAgICAgbWF0Y2hbNF0gKyBtYXRjaFs0XSxcbiAgICAgIC8vIGFcbiAgICAgIDE2XG4gICAgKSA+Pj4gMDtcbiAgfVxuICBpZiAobWF0Y2ggPSBoc2wuZXhlYyhjb2xvcikpIHtcbiAgICByZXR1cm4gKGhzbFRvUmdiKFxuICAgICAgcGFyc2UzNjAobWF0Y2hbMV0pLFxuICAgICAgLy8gaFxuICAgICAgcGFyc2VQZXJjZW50YWdlKG1hdGNoWzJdKSxcbiAgICAgIC8vIHNcbiAgICAgIHBhcnNlUGVyY2VudGFnZShtYXRjaFszXSlcbiAgICAgIC8vIGxcbiAgICApIHwgMjU1KSA+Pj4gLy8gYVxuICAgIDA7XG4gIH1cbiAgaWYgKG1hdGNoID0gaHNsYS5leGVjKGNvbG9yKSkge1xuICAgIHJldHVybiAoaHNsVG9SZ2IoXG4gICAgICBwYXJzZTM2MChtYXRjaFsxXSksXG4gICAgICAvLyBoXG4gICAgICBwYXJzZVBlcmNlbnRhZ2UobWF0Y2hbMl0pLFxuICAgICAgLy8gc1xuICAgICAgcGFyc2VQZXJjZW50YWdlKG1hdGNoWzNdKVxuICAgICAgLy8gbFxuICAgICkgfCBwYXJzZTEobWF0Y2hbNF0pKSA+Pj4gLy8gYVxuICAgIDA7XG4gIH1cbiAgcmV0dXJuIG51bGw7XG59XG5mdW5jdGlvbiBodWUycmdiKHAsIHEsIHQpIHtcbiAgaWYgKHQgPCAwKVxuICAgIHQgKz0gMTtcbiAgaWYgKHQgPiAxKVxuICAgIHQgLT0gMTtcbiAgaWYgKHQgPCAxIC8gNilcbiAgICByZXR1cm4gcCArIChxIC0gcCkgKiA2ICogdDtcbiAgaWYgKHQgPCAxIC8gMilcbiAgICByZXR1cm4gcTtcbiAgaWYgKHQgPCAyIC8gMylcbiAgICByZXR1cm4gcCArIChxIC0gcCkgKiAoMiAvIDMgLSB0KSAqIDY7XG4gIHJldHVybiBwO1xufVxuZnVuY3Rpb24gaHNsVG9SZ2IoaCwgcywgbCkge1xuICBjb25zdCBxID0gbCA8IDAuNSA/IGwgKiAoMSArIHMpIDogbCArIHMgLSBsICogcztcbiAgY29uc3QgcCA9IDIgKiBsIC0gcTtcbiAgY29uc3QgciA9IGh1ZTJyZ2IocCwgcSwgaCArIDEgLyAzKTtcbiAgY29uc3QgZyA9IGh1ZTJyZ2IocCwgcSwgaCk7XG4gIGNvbnN0IGIgPSBodWUycmdiKHAsIHEsIGggLSAxIC8gMyk7XG4gIHJldHVybiBNYXRoLnJvdW5kKHIgKiAyNTUpIDw8IDI0IHwgTWF0aC5yb3VuZChnICogMjU1KSA8PCAxNiB8IE1hdGgucm91bmQoYiAqIDI1NSkgPDwgODtcbn1cbmZ1bmN0aW9uIHBhcnNlMjU1KHN0cikge1xuICBjb25zdCBpbnQgPSBwYXJzZUludChzdHIsIDEwKTtcbiAgaWYgKGludCA8IDApXG4gICAgcmV0dXJuIDA7XG4gIGlmIChpbnQgPiAyNTUpXG4gICAgcmV0dXJuIDI1NTtcbiAgcmV0dXJuIGludDtcbn1cbmZ1bmN0aW9uIHBhcnNlMzYwKHN0cikge1xuICBjb25zdCBpbnQgPSBwYXJzZUZsb2F0KHN0cik7XG4gIHJldHVybiAoaW50ICUgMzYwICsgMzYwKSAlIDM2MCAvIDM2MDtcbn1cbmZ1bmN0aW9uIHBhcnNlMShzdHIpIHtcbiAgY29uc3QgbnVtID0gcGFyc2VGbG9hdChzdHIpO1xuICBpZiAobnVtIDwgMClcbiAgICByZXR1cm4gMDtcbiAgaWYgKG51bSA+IDEpXG4gICAgcmV0dXJuIDI1NTtcbiAgcmV0dXJuIE1hdGgucm91bmQobnVtICogMjU1KTtcbn1cbmZ1bmN0aW9uIHBhcnNlUGVyY2VudGFnZShzdHIpIHtcbiAgY29uc3QgaW50ID0gcGFyc2VGbG9hdChzdHIpO1xuICBpZiAoaW50IDwgMClcbiAgICByZXR1cm4gMDtcbiAgaWYgKGludCA+IDEwMClcbiAgICByZXR1cm4gMTtcbiAgcmV0dXJuIGludCAvIDEwMDtcbn1cblxuLy8gc3JjL2NvbG9yVG9SZ2JhLnRzXG5mdW5jdGlvbiBjb2xvclRvUmdiYShpbnB1dCkge1xuICBsZXQgaW50MzJDb2xvciA9IG5vcm1hbGl6ZUNvbG9yKGlucHV0KTtcbiAgaWYgKGludDMyQ29sb3IgPT09IG51bGwpXG4gICAgcmV0dXJuIGlucHV0O1xuICBpbnQzMkNvbG9yID0gaW50MzJDb2xvciB8fCAwO1xuICBjb25zdCByID0gKGludDMyQ29sb3IgJiA0Mjc4MTkwMDgwKSA+Pj4gMjQ7XG4gIGNvbnN0IGcgPSAoaW50MzJDb2xvciAmIDE2NzExNjgwKSA+Pj4gMTY7XG4gIGNvbnN0IGIgPSAoaW50MzJDb2xvciAmIDY1MjgwKSA+Pj4gODtcbiAgY29uc3QgYSA9IChpbnQzMkNvbG9yICYgMjU1KSAvIDI1NTtcbiAgcmV0dXJuIGByZ2JhKCR7cn0sICR7Z30sICR7Yn0sICR7YX0pYDtcbn1cblxuLy8gc3JjL2NyZWF0ZUludGVycG9sYXRvci50c1xudmFyIGNyZWF0ZUludGVycG9sYXRvciA9IChyYW5nZSwgb3V0cHV0LCBleHRyYXBvbGF0ZSkgPT4ge1xuICBpZiAoaXMuZnVuKHJhbmdlKSkge1xuICAgIHJldHVybiByYW5nZTtcbiAgfVxuICBpZiAoaXMuYXJyKHJhbmdlKSkge1xuICAgIHJldHVybiBjcmVhdGVJbnRlcnBvbGF0b3Ioe1xuICAgICAgcmFuZ2UsXG4gICAgICBvdXRwdXQsXG4gICAgICBleHRyYXBvbGF0ZVxuICAgIH0pO1xuICB9XG4gIGlmIChpcy5zdHIocmFuZ2Uub3V0cHV0WzBdKSkge1xuICAgIHJldHVybiBjcmVhdGVTdHJpbmdJbnRlcnBvbGF0b3IocmFuZ2UpO1xuICB9XG4gIGNvbnN0IGNvbmZpZyA9IHJhbmdlO1xuICBjb25zdCBvdXRwdXRSYW5nZSA9IGNvbmZpZy5vdXRwdXQ7XG4gIGNvbnN0IGlucHV0UmFuZ2UgPSBjb25maWcucmFuZ2UgfHwgWzAsIDFdO1xuICBjb25zdCBleHRyYXBvbGF0ZUxlZnQgPSBjb25maWcuZXh0cmFwb2xhdGVMZWZ0IHx8IGNvbmZpZy5leHRyYXBvbGF0ZSB8fCBcImV4dGVuZFwiO1xuICBjb25zdCBleHRyYXBvbGF0ZVJpZ2h0ID0gY29uZmlnLmV4dHJhcG9sYXRlUmlnaHQgfHwgY29uZmlnLmV4dHJhcG9sYXRlIHx8IFwiZXh0ZW5kXCI7XG4gIGNvbnN0IGVhc2luZyA9IGNvbmZpZy5lYXNpbmcgfHwgKCh0KSA9PiB0KTtcbiAgcmV0dXJuIChpbnB1dCkgPT4ge1xuICAgIGNvbnN0IHJhbmdlMiA9IGZpbmRSYW5nZShpbnB1dCwgaW5wdXRSYW5nZSk7XG4gICAgcmV0dXJuIGludGVycG9sYXRlKFxuICAgICAgaW5wdXQsXG4gICAgICBpbnB1dFJhbmdlW3JhbmdlMl0sXG4gICAgICBpbnB1dFJhbmdlW3JhbmdlMiArIDFdLFxuICAgICAgb3V0cHV0UmFuZ2VbcmFuZ2UyXSxcbiAgICAgIG91dHB1dFJhbmdlW3JhbmdlMiArIDFdLFxuICAgICAgZWFzaW5nLFxuICAgICAgZXh0cmFwb2xhdGVMZWZ0LFxuICAgICAgZXh0cmFwb2xhdGVSaWdodCxcbiAgICAgIGNvbmZpZy5tYXBcbiAgICApO1xuICB9O1xufTtcbmZ1bmN0aW9uIGludGVycG9sYXRlKGlucHV0LCBpbnB1dE1pbiwgaW5wdXRNYXgsIG91dHB1dE1pbiwgb3V0cHV0TWF4LCBlYXNpbmcsIGV4dHJhcG9sYXRlTGVmdCwgZXh0cmFwb2xhdGVSaWdodCwgbWFwKSB7XG4gIGxldCByZXN1bHQgPSBtYXAgPyBtYXAoaW5wdXQpIDogaW5wdXQ7XG4gIGlmIChyZXN1bHQgPCBpbnB1dE1pbikge1xuICAgIGlmIChleHRyYXBvbGF0ZUxlZnQgPT09IFwiaWRlbnRpdHlcIilcbiAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgZWxzZSBpZiAoZXh0cmFwb2xhdGVMZWZ0ID09PSBcImNsYW1wXCIpXG4gICAgICByZXN1bHQgPSBpbnB1dE1pbjtcbiAgfVxuICBpZiAocmVzdWx0ID4gaW5wdXRNYXgpIHtcbiAgICBpZiAoZXh0cmFwb2xhdGVSaWdodCA9PT0gXCJpZGVudGl0eVwiKVxuICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICBlbHNlIGlmIChleHRyYXBvbGF0ZVJpZ2h0ID09PSBcImNsYW1wXCIpXG4gICAgICByZXN1bHQgPSBpbnB1dE1heDtcbiAgfVxuICBpZiAob3V0cHV0TWluID09PSBvdXRwdXRNYXgpXG4gICAgcmV0dXJuIG91dHB1dE1pbjtcbiAgaWYgKGlucHV0TWluID09PSBpbnB1dE1heClcbiAgICByZXR1cm4gaW5wdXQgPD0gaW5wdXRNaW4gPyBvdXRwdXRNaW4gOiBvdXRwdXRNYXg7XG4gIGlmIChpbnB1dE1pbiA9PT0gLUluZmluaXR5KVxuICAgIHJlc3VsdCA9IC1yZXN1bHQ7XG4gIGVsc2UgaWYgKGlucHV0TWF4ID09PSBJbmZpbml0eSlcbiAgICByZXN1bHQgPSByZXN1bHQgLSBpbnB1dE1pbjtcbiAgZWxzZVxuICAgIHJlc3VsdCA9IChyZXN1bHQgLSBpbnB1dE1pbikgLyAoaW5wdXRNYXggLSBpbnB1dE1pbik7XG4gIHJlc3VsdCA9IGVhc2luZyhyZXN1bHQpO1xuICBpZiAob3V0cHV0TWluID09PSAtSW5maW5pdHkpXG4gICAgcmVzdWx0ID0gLXJlc3VsdDtcbiAgZWxzZSBpZiAob3V0cHV0TWF4ID09PSBJbmZpbml0eSlcbiAgICByZXN1bHQgPSByZXN1bHQgKyBvdXRwdXRNaW47XG4gIGVsc2VcbiAgICByZXN1bHQgPSByZXN1bHQgKiAob3V0cHV0TWF4IC0gb3V0cHV0TWluKSArIG91dHB1dE1pbjtcbiAgcmV0dXJuIHJlc3VsdDtcbn1cbmZ1bmN0aW9uIGZpbmRSYW5nZShpbnB1dCwgaW5wdXRSYW5nZSkge1xuICBmb3IgKHZhciBpID0gMTsgaSA8IGlucHV0UmFuZ2UubGVuZ3RoIC0gMTsgKytpKVxuICAgIGlmIChpbnB1dFJhbmdlW2ldID49IGlucHV0KVxuICAgICAgYnJlYWs7XG4gIHJldHVybiBpIC0gMTtcbn1cblxuLy8gc3JjL2Vhc2luZ3MudHNcbnZhciBzdGVwcyA9IChzdGVwczIsIGRpcmVjdGlvbiA9IFwiZW5kXCIpID0+IChwcm9ncmVzczIpID0+IHtcbiAgcHJvZ3Jlc3MyID0gZGlyZWN0aW9uID09PSBcImVuZFwiID8gTWF0aC5taW4ocHJvZ3Jlc3MyLCAwLjk5OSkgOiBNYXRoLm1heChwcm9ncmVzczIsIDFlLTMpO1xuICBjb25zdCBleHBhbmRlZCA9IHByb2dyZXNzMiAqIHN0ZXBzMjtcbiAgY29uc3Qgcm91bmRlZCA9IGRpcmVjdGlvbiA9PT0gXCJlbmRcIiA/IE1hdGguZmxvb3IoZXhwYW5kZWQpIDogTWF0aC5jZWlsKGV4cGFuZGVkKTtcbiAgcmV0dXJuIGNsYW1wKDAsIDEsIHJvdW5kZWQgLyBzdGVwczIpO1xufTtcbnZhciBjMSA9IDEuNzAxNTg7XG52YXIgYzIgPSBjMSAqIDEuNTI1O1xudmFyIGMzID0gYzEgKyAxO1xudmFyIGM0ID0gMiAqIE1hdGguUEkgLyAzO1xudmFyIGM1ID0gMiAqIE1hdGguUEkgLyA0LjU7XG52YXIgYm91bmNlT3V0ID0gKHgpID0+IHtcbiAgY29uc3QgbjEgPSA3LjU2MjU7XG4gIGNvbnN0IGQxID0gMi43NTtcbiAgaWYgKHggPCAxIC8gZDEpIHtcbiAgICByZXR1cm4gbjEgKiB4ICogeDtcbiAgfSBlbHNlIGlmICh4IDwgMiAvIGQxKSB7XG4gICAgcmV0dXJuIG4xICogKHggLT0gMS41IC8gZDEpICogeCArIDAuNzU7XG4gIH0gZWxzZSBpZiAoeCA8IDIuNSAvIGQxKSB7XG4gICAgcmV0dXJuIG4xICogKHggLT0gMi4yNSAvIGQxKSAqIHggKyAwLjkzNzU7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIG4xICogKHggLT0gMi42MjUgLyBkMSkgKiB4ICsgMC45ODQzNzU7XG4gIH1cbn07XG52YXIgZWFzaW5ncyA9IHtcbiAgbGluZWFyOiAoeCkgPT4geCxcbiAgZWFzZUluUXVhZDogKHgpID0+IHggKiB4LFxuICBlYXNlT3V0UXVhZDogKHgpID0+IDEgLSAoMSAtIHgpICogKDEgLSB4KSxcbiAgZWFzZUluT3V0UXVhZDogKHgpID0+IHggPCAwLjUgPyAyICogeCAqIHggOiAxIC0gTWF0aC5wb3coLTIgKiB4ICsgMiwgMikgLyAyLFxuICBlYXNlSW5DdWJpYzogKHgpID0+IHggKiB4ICogeCxcbiAgZWFzZU91dEN1YmljOiAoeCkgPT4gMSAtIE1hdGgucG93KDEgLSB4LCAzKSxcbiAgZWFzZUluT3V0Q3ViaWM6ICh4KSA9PiB4IDwgMC41ID8gNCAqIHggKiB4ICogeCA6IDEgLSBNYXRoLnBvdygtMiAqIHggKyAyLCAzKSAvIDIsXG4gIGVhc2VJblF1YXJ0OiAoeCkgPT4geCAqIHggKiB4ICogeCxcbiAgZWFzZU91dFF1YXJ0OiAoeCkgPT4gMSAtIE1hdGgucG93KDEgLSB4LCA0KSxcbiAgZWFzZUluT3V0UXVhcnQ6ICh4KSA9PiB4IDwgMC41ID8gOCAqIHggKiB4ICogeCAqIHggOiAxIC0gTWF0aC5wb3coLTIgKiB4ICsgMiwgNCkgLyAyLFxuICBlYXNlSW5RdWludDogKHgpID0+IHggKiB4ICogeCAqIHggKiB4LFxuICBlYXNlT3V0UXVpbnQ6ICh4KSA9PiAxIC0gTWF0aC5wb3coMSAtIHgsIDUpLFxuICBlYXNlSW5PdXRRdWludDogKHgpID0+IHggPCAwLjUgPyAxNiAqIHggKiB4ICogeCAqIHggKiB4IDogMSAtIE1hdGgucG93KC0yICogeCArIDIsIDUpIC8gMixcbiAgZWFzZUluU2luZTogKHgpID0+IDEgLSBNYXRoLmNvcyh4ICogTWF0aC5QSSAvIDIpLFxuICBlYXNlT3V0U2luZTogKHgpID0+IE1hdGguc2luKHggKiBNYXRoLlBJIC8gMiksXG4gIGVhc2VJbk91dFNpbmU6ICh4KSA9PiAtKE1hdGguY29zKE1hdGguUEkgKiB4KSAtIDEpIC8gMixcbiAgZWFzZUluRXhwbzogKHgpID0+IHggPT09IDAgPyAwIDogTWF0aC5wb3coMiwgMTAgKiB4IC0gMTApLFxuICBlYXNlT3V0RXhwbzogKHgpID0+IHggPT09IDEgPyAxIDogMSAtIE1hdGgucG93KDIsIC0xMCAqIHgpLFxuICBlYXNlSW5PdXRFeHBvOiAoeCkgPT4geCA9PT0gMCA/IDAgOiB4ID09PSAxID8gMSA6IHggPCAwLjUgPyBNYXRoLnBvdygyLCAyMCAqIHggLSAxMCkgLyAyIDogKDIgLSBNYXRoLnBvdygyLCAtMjAgKiB4ICsgMTApKSAvIDIsXG4gIGVhc2VJbkNpcmM6ICh4KSA9PiAxIC0gTWF0aC5zcXJ0KDEgLSBNYXRoLnBvdyh4LCAyKSksXG4gIGVhc2VPdXRDaXJjOiAoeCkgPT4gTWF0aC5zcXJ0KDEgLSBNYXRoLnBvdyh4IC0gMSwgMikpLFxuICBlYXNlSW5PdXRDaXJjOiAoeCkgPT4geCA8IDAuNSA/ICgxIC0gTWF0aC5zcXJ0KDEgLSBNYXRoLnBvdygyICogeCwgMikpKSAvIDIgOiAoTWF0aC5zcXJ0KDEgLSBNYXRoLnBvdygtMiAqIHggKyAyLCAyKSkgKyAxKSAvIDIsXG4gIGVhc2VJbkJhY2s6ICh4KSA9PiBjMyAqIHggKiB4ICogeCAtIGMxICogeCAqIHgsXG4gIGVhc2VPdXRCYWNrOiAoeCkgPT4gMSArIGMzICogTWF0aC5wb3coeCAtIDEsIDMpICsgYzEgKiBNYXRoLnBvdyh4IC0gMSwgMiksXG4gIGVhc2VJbk91dEJhY2s6ICh4KSA9PiB4IDwgMC41ID8gTWF0aC5wb3coMiAqIHgsIDIpICogKChjMiArIDEpICogMiAqIHggLSBjMikgLyAyIDogKE1hdGgucG93KDIgKiB4IC0gMiwgMikgKiAoKGMyICsgMSkgKiAoeCAqIDIgLSAyKSArIGMyKSArIDIpIC8gMixcbiAgZWFzZUluRWxhc3RpYzogKHgpID0+IHggPT09IDAgPyAwIDogeCA9PT0gMSA/IDEgOiAtTWF0aC5wb3coMiwgMTAgKiB4IC0gMTApICogTWF0aC5zaW4oKHggKiAxMCAtIDEwLjc1KSAqIGM0KSxcbiAgZWFzZU91dEVsYXN0aWM6ICh4KSA9PiB4ID09PSAwID8gMCA6IHggPT09IDEgPyAxIDogTWF0aC5wb3coMiwgLTEwICogeCkgKiBNYXRoLnNpbigoeCAqIDEwIC0gMC43NSkgKiBjNCkgKyAxLFxuICBlYXNlSW5PdXRFbGFzdGljOiAoeCkgPT4geCA9PT0gMCA/IDAgOiB4ID09PSAxID8gMSA6IHggPCAwLjUgPyAtKE1hdGgucG93KDIsIDIwICogeCAtIDEwKSAqIE1hdGguc2luKCgyMCAqIHggLSAxMS4xMjUpICogYzUpKSAvIDIgOiBNYXRoLnBvdygyLCAtMjAgKiB4ICsgMTApICogTWF0aC5zaW4oKDIwICogeCAtIDExLjEyNSkgKiBjNSkgLyAyICsgMSxcbiAgZWFzZUluQm91bmNlOiAoeCkgPT4gMSAtIGJvdW5jZU91dCgxIC0geCksXG4gIGVhc2VPdXRCb3VuY2U6IGJvdW5jZU91dCxcbiAgZWFzZUluT3V0Qm91bmNlOiAoeCkgPT4geCA8IDAuNSA/ICgxIC0gYm91bmNlT3V0KDEgLSAyICogeCkpIC8gMiA6ICgxICsgYm91bmNlT3V0KDIgKiB4IC0gMSkpIC8gMixcbiAgc3RlcHNcbn07XG5cbi8vIHNyYy9mbHVpZHMudHNcbnZhciAkZ2V0ID0gU3ltYm9sLmZvcihcIkZsdWlkVmFsdWUuZ2V0XCIpO1xudmFyICRvYnNlcnZlcnMgPSBTeW1ib2wuZm9yKFwiRmx1aWRWYWx1ZS5vYnNlcnZlcnNcIik7XG52YXIgaGFzRmx1aWRWYWx1ZSA9IChhcmcpID0+IEJvb2xlYW4oYXJnICYmIGFyZ1skZ2V0XSk7XG52YXIgZ2V0Rmx1aWRWYWx1ZSA9IChhcmcpID0+IGFyZyAmJiBhcmdbJGdldF0gPyBhcmdbJGdldF0oKSA6IGFyZztcbnZhciBnZXRGbHVpZE9ic2VydmVycyA9ICh0YXJnZXQpID0+IHRhcmdldFskb2JzZXJ2ZXJzXSB8fCBudWxsO1xuZnVuY3Rpb24gY2FsbEZsdWlkT2JzZXJ2ZXIob2JzZXJ2ZXIyLCBldmVudCkge1xuICBpZiAob2JzZXJ2ZXIyLmV2ZW50T2JzZXJ2ZWQpIHtcbiAgICBvYnNlcnZlcjIuZXZlbnRPYnNlcnZlZChldmVudCk7XG4gIH0gZWxzZSB7XG4gICAgb2JzZXJ2ZXIyKGV2ZW50KTtcbiAgfVxufVxuZnVuY3Rpb24gY2FsbEZsdWlkT2JzZXJ2ZXJzKHRhcmdldCwgZXZlbnQpIHtcbiAgY29uc3Qgb2JzZXJ2ZXJzID0gdGFyZ2V0WyRvYnNlcnZlcnNdO1xuICBpZiAob2JzZXJ2ZXJzKSB7XG4gICAgb2JzZXJ2ZXJzLmZvckVhY2goKG9ic2VydmVyMikgPT4ge1xuICAgICAgY2FsbEZsdWlkT2JzZXJ2ZXIob2JzZXJ2ZXIyLCBldmVudCk7XG4gICAgfSk7XG4gIH1cbn1cbnZhciBGbHVpZFZhbHVlID0gY2xhc3Mge1xuICBjb25zdHJ1Y3RvcihnZXQpIHtcbiAgICBpZiAoIWdldCAmJiAhKGdldCA9IHRoaXMuZ2V0KSkge1xuICAgICAgdGhyb3cgRXJyb3IoXCJVbmtub3duIGdldHRlclwiKTtcbiAgICB9XG4gICAgc2V0Rmx1aWRHZXR0ZXIodGhpcywgZ2V0KTtcbiAgfVxufTtcbiRnZXQsICRvYnNlcnZlcnM7XG52YXIgc2V0Rmx1aWRHZXR0ZXIgPSAodGFyZ2V0LCBnZXQpID0+IHNldEhpZGRlbih0YXJnZXQsICRnZXQsIGdldCk7XG5mdW5jdGlvbiBhZGRGbHVpZE9ic2VydmVyKHRhcmdldCwgb2JzZXJ2ZXIyKSB7XG4gIGlmICh0YXJnZXRbJGdldF0pIHtcbiAgICBsZXQgb2JzZXJ2ZXJzID0gdGFyZ2V0WyRvYnNlcnZlcnNdO1xuICAgIGlmICghb2JzZXJ2ZXJzKSB7XG4gICAgICBzZXRIaWRkZW4odGFyZ2V0LCAkb2JzZXJ2ZXJzLCBvYnNlcnZlcnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFNldCgpKTtcbiAgICB9XG4gICAgaWYgKCFvYnNlcnZlcnMuaGFzKG9ic2VydmVyMikpIHtcbiAgICAgIG9ic2VydmVycy5hZGQob2JzZXJ2ZXIyKTtcbiAgICAgIGlmICh0YXJnZXQub2JzZXJ2ZXJBZGRlZCkge1xuICAgICAgICB0YXJnZXQub2JzZXJ2ZXJBZGRlZChvYnNlcnZlcnMuc2l6ZSwgb2JzZXJ2ZXIyKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIG9ic2VydmVyMjtcbn1cbmZ1bmN0aW9uIHJlbW92ZUZsdWlkT2JzZXJ2ZXIodGFyZ2V0LCBvYnNlcnZlcjIpIHtcbiAgY29uc3Qgb2JzZXJ2ZXJzID0gdGFyZ2V0WyRvYnNlcnZlcnNdO1xuICBpZiAob2JzZXJ2ZXJzICYmIG9ic2VydmVycy5oYXMob2JzZXJ2ZXIyKSkge1xuICAgIGNvbnN0IGNvdW50ID0gb2JzZXJ2ZXJzLnNpemUgLSAxO1xuICAgIGlmIChjb3VudCkge1xuICAgICAgb2JzZXJ2ZXJzLmRlbGV0ZShvYnNlcnZlcjIpO1xuICAgIH0gZWxzZSB7XG4gICAgICB0YXJnZXRbJG9ic2VydmVyc10gPSBudWxsO1xuICAgIH1cbiAgICBpZiAodGFyZ2V0Lm9ic2VydmVyUmVtb3ZlZCkge1xuICAgICAgdGFyZ2V0Lm9ic2VydmVyUmVtb3ZlZChjb3VudCwgb2JzZXJ2ZXIyKTtcbiAgICB9XG4gIH1cbn1cbnZhciBzZXRIaWRkZW4gPSAodGFyZ2V0LCBrZXksIHZhbHVlKSA9PiBPYmplY3QuZGVmaW5lUHJvcGVydHkodGFyZ2V0LCBrZXksIHtcbiAgdmFsdWUsXG4gIHdyaXRhYmxlOiB0cnVlLFxuICBjb25maWd1cmFibGU6IHRydWVcbn0pO1xuXG4vLyBzcmMvcmVnZXhzLnRzXG52YXIgbnVtYmVyUmVnZXggPSAvWytcXC1dPyg/OjB8WzEtOV1cXGQqKSg/OlxcLlxcZCopPyg/OltlRV1bK1xcLV0/XFxkKyk/L2c7XG52YXIgY29sb3JSZWdleCA9IC8oIyg/OlswLTlhLWZdezJ9KXsyLDR9fCgjWzAtOWEtZl17M30pfChyZ2J8aHNsKWE/XFwoKC0/XFxkKyU/WyxcXHNdKyl7MiwzfVxccypbXFxkXFwuXSslP1xcKSkvZ2k7XG52YXIgdW5pdFJlZ2V4ID0gbmV3IFJlZ0V4cChgKCR7bnVtYmVyUmVnZXguc291cmNlfSkoJXxbYS16XSspYCwgXCJpXCIpO1xudmFyIHJnYmFSZWdleCA9IC9yZ2JhXFwoKFswLTlcXC4tXSspLCAoWzAtOVxcLi1dKyksIChbMC05XFwuLV0rKSwgKFswLTlcXC4tXSspXFwpL2dpO1xudmFyIGNzc1ZhcmlhYmxlUmVnZXggPSAvdmFyXFwoKC0tW2EtekEtWjAtOS1fXSspLD8gPyhbYS16QS1aMC05ICgpJSMuLC1dKyk/XFwpLztcblxuLy8gc3JjL3ZhcmlhYmxlVG9SZ2JhLnRzXG52YXIgdmFyaWFibGVUb1JnYmEgPSAoaW5wdXQpID0+IHtcbiAgY29uc3QgW3Rva2VuLCBmYWxsYmFja10gPSBwYXJzZUNTU1ZhcmlhYmxlKGlucHV0KTtcbiAgaWYgKCF0b2tlbiB8fCBpc1NTUigpKSB7XG4gICAgcmV0dXJuIGlucHV0O1xuICB9XG4gIGNvbnN0IHZhbHVlID0gd2luZG93LmdldENvbXB1dGVkU3R5bGUoZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50KS5nZXRQcm9wZXJ0eVZhbHVlKHRva2VuKTtcbiAgaWYgKHZhbHVlKSB7XG4gICAgcmV0dXJuIHZhbHVlLnRyaW0oKTtcbiAgfSBlbHNlIGlmIChmYWxsYmFjayAmJiBmYWxsYmFjay5zdGFydHNXaXRoKFwiLS1cIikpIHtcbiAgICBjb25zdCB2YWx1ZTIgPSB3aW5kb3cuZ2V0Q29tcHV0ZWRTdHlsZShkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQpLmdldFByb3BlcnR5VmFsdWUoZmFsbGJhY2spO1xuICAgIGlmICh2YWx1ZTIpIHtcbiAgICAgIHJldHVybiB2YWx1ZTI7XG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiBpbnB1dDtcbiAgICB9XG4gIH0gZWxzZSBpZiAoZmFsbGJhY2sgJiYgY3NzVmFyaWFibGVSZWdleC50ZXN0KGZhbGxiYWNrKSkge1xuICAgIHJldHVybiB2YXJpYWJsZVRvUmdiYShmYWxsYmFjayk7XG4gIH0gZWxzZSBpZiAoZmFsbGJhY2spIHtcbiAgICByZXR1cm4gZmFsbGJhY2s7XG4gIH1cbiAgcmV0dXJuIGlucHV0O1xufTtcbnZhciBwYXJzZUNTU1ZhcmlhYmxlID0gKGN1cnJlbnQpID0+IHtcbiAgY29uc3QgbWF0Y2ggPSBjc3NWYXJpYWJsZVJlZ2V4LmV4ZWMoY3VycmVudCk7XG4gIGlmICghbWF0Y2gpXG4gICAgcmV0dXJuIFssXTtcbiAgY29uc3QgWywgdG9rZW4sIGZhbGxiYWNrXSA9IG1hdGNoO1xuICByZXR1cm4gW3Rva2VuLCBmYWxsYmFja107XG59O1xuXG4vLyBzcmMvc3RyaW5nSW50ZXJwb2xhdGlvbi50c1xudmFyIG5hbWVkQ29sb3JSZWdleDtcbnZhciByZ2JhUm91bmQgPSAoXywgcDEsIHAyLCBwMywgcDQpID0+IGByZ2JhKCR7TWF0aC5yb3VuZChwMSl9LCAke01hdGgucm91bmQocDIpfSwgJHtNYXRoLnJvdW5kKHAzKX0sICR7cDR9KWA7XG52YXIgY3JlYXRlU3RyaW5nSW50ZXJwb2xhdG9yMiA9IChjb25maWcpID0+IHtcbiAgaWYgKCFuYW1lZENvbG9yUmVnZXgpXG4gICAgbmFtZWRDb2xvclJlZ2V4ID0gY29sb3JzID8gKFxuICAgICAgLy8gbWF0Y2ggY29sb3IgbmFtZXMsIGlnbm9yZSBwYXJ0aWFsIG1hdGNoZXNcbiAgICAgIG5ldyBSZWdFeHAoYCgke09iamVjdC5rZXlzKGNvbG9ycykuam9pbihcInxcIil9KSg/IVxcXFx3KWAsIFwiZ1wiKVxuICAgICkgOiAoXG4gICAgICAvLyBuZXZlciBtYXRjaFxuICAgICAgL15cXGIkL1xuICAgICk7XG4gIGNvbnN0IG91dHB1dCA9IGNvbmZpZy5vdXRwdXQubWFwKCh2YWx1ZSkgPT4ge1xuICAgIHJldHVybiBnZXRGbHVpZFZhbHVlKHZhbHVlKS5yZXBsYWNlKGNzc1ZhcmlhYmxlUmVnZXgsIHZhcmlhYmxlVG9SZ2JhKS5yZXBsYWNlKGNvbG9yUmVnZXgsIGNvbG9yVG9SZ2JhKS5yZXBsYWNlKG5hbWVkQ29sb3JSZWdleCwgY29sb3JUb1JnYmEpO1xuICB9KTtcbiAgY29uc3Qga2V5ZnJhbWVzID0gb3V0cHV0Lm1hcCgodmFsdWUpID0+IHZhbHVlLm1hdGNoKG51bWJlclJlZ2V4KS5tYXAoTnVtYmVyKSk7XG4gIGNvbnN0IG91dHB1dFJhbmdlcyA9IGtleWZyYW1lc1swXS5tYXAoXG4gICAgKF8sIGkpID0+IGtleWZyYW1lcy5tYXAoKHZhbHVlcykgPT4ge1xuICAgICAgaWYgKCEoaSBpbiB2YWx1ZXMpKSB7XG4gICAgICAgIHRocm93IEVycm9yKCdUaGUgYXJpdHkgb2YgZWFjaCBcIm91dHB1dFwiIHZhbHVlIG11c3QgYmUgZXF1YWwnKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiB2YWx1ZXNbaV07XG4gICAgfSlcbiAgKTtcbiAgY29uc3QgaW50ZXJwb2xhdG9ycyA9IG91dHB1dFJhbmdlcy5tYXAoXG4gICAgKG91dHB1dDIpID0+IGNyZWF0ZUludGVycG9sYXRvcih7IC4uLmNvbmZpZywgb3V0cHV0OiBvdXRwdXQyIH0pXG4gICk7XG4gIHJldHVybiAoaW5wdXQpID0+IHtcbiAgICBjb25zdCBtaXNzaW5nVW5pdCA9ICF1bml0UmVnZXgudGVzdChvdXRwdXRbMF0pICYmIG91dHB1dC5maW5kKCh2YWx1ZSkgPT4gdW5pdFJlZ2V4LnRlc3QodmFsdWUpKT8ucmVwbGFjZShudW1iZXJSZWdleCwgXCJcIik7XG4gICAgbGV0IGkgPSAwO1xuICAgIHJldHVybiBvdXRwdXRbMF0ucmVwbGFjZShcbiAgICAgIG51bWJlclJlZ2V4LFxuICAgICAgKCkgPT4gYCR7aW50ZXJwb2xhdG9yc1tpKytdKGlucHV0KX0ke21pc3NpbmdVbml0IHx8IFwiXCJ9YFxuICAgICkucmVwbGFjZShyZ2JhUmVnZXgsIHJnYmFSb3VuZCk7XG4gIH07XG59O1xuXG4vLyBzcmMvZGVwcmVjYXRpb25zLnRzXG52YXIgcHJlZml4ID0gXCJyZWFjdC1zcHJpbmc6IFwiO1xudmFyIG9uY2UgPSAoZm4pID0+IHtcbiAgY29uc3QgZnVuYyA9IGZuO1xuICBsZXQgY2FsbGVkID0gZmFsc2U7XG4gIGlmICh0eXBlb2YgZnVuYyAhPSBcImZ1bmN0aW9uXCIpIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKGAke3ByZWZpeH1vbmNlIHJlcXVpcmVzIGEgZnVuY3Rpb24gcGFyYW1ldGVyYCk7XG4gIH1cbiAgcmV0dXJuICguLi5hcmdzKSA9PiB7XG4gICAgaWYgKCFjYWxsZWQpIHtcbiAgICAgIGZ1bmMoLi4uYXJncyk7XG4gICAgICBjYWxsZWQgPSB0cnVlO1xuICAgIH1cbiAgfTtcbn07XG52YXIgd2FybkludGVycG9sYXRlID0gb25jZShjb25zb2xlLndhcm4pO1xuZnVuY3Rpb24gZGVwcmVjYXRlSW50ZXJwb2xhdGUoKSB7XG4gIHdhcm5JbnRlcnBvbGF0ZShcbiAgICBgJHtwcmVmaXh9VGhlIFwiaW50ZXJwb2xhdGVcIiBmdW5jdGlvbiBpcyBkZXByZWNhdGVkIGluIHY5ICh1c2UgXCJ0b1wiIGluc3RlYWQpYFxuICApO1xufVxudmFyIHdhcm5EaXJlY3RDYWxsID0gb25jZShjb25zb2xlLndhcm4pO1xuZnVuY3Rpb24gZGVwcmVjYXRlRGlyZWN0Q2FsbCgpIHtcbiAgd2FybkRpcmVjdENhbGwoXG4gICAgYCR7cHJlZml4fURpcmVjdGx5IGNhbGxpbmcgc3RhcnQgaW5zdGVhZCBvZiB1c2luZyB0aGUgYXBpIG9iamVjdCBpcyBkZXByZWNhdGVkIGluIHY5ICh1c2UgXCIuc3RhcnRcIiBpbnN0ZWFkKSwgdGhpcyB3aWxsIGJlIHJlbW92ZWQgaW4gbGF0ZXIgMC5YLjAgdmVyc2lvbnNgXG4gICk7XG59XG5cbi8vIHNyYy9pc0FuaW1hdGVkU3RyaW5nLnRzXG5mdW5jdGlvbiBpc0FuaW1hdGVkU3RyaW5nKHZhbHVlKSB7XG4gIHJldHVybiBpcy5zdHIodmFsdWUpICYmICh2YWx1ZVswXSA9PSBcIiNcIiB8fCAvXFxkLy50ZXN0KHZhbHVlKSB8fCAvLyBEbyBub3QgaWRlbnRpZnkgYSBDU1MgdmFyaWFibGUgYXMgYW4gQW5pbWF0ZWRTdHJpbmcgaWYgaXRzIFNTUlxuICAhaXNTU1IoKSAmJiBjc3NWYXJpYWJsZVJlZ2V4LnRlc3QodmFsdWUpIHx8IHZhbHVlIGluIChjb2xvcnMgfHwge30pKTtcbn1cblxuLy8gc3JjL2RvbS1ldmVudHMvc2Nyb2xsL2luZGV4LnRzXG5pbXBvcnQgeyByYWYgYXMgcmFmMyB9IGZyb20gXCJAcmVhY3Qtc3ByaW5nL3JhZnpcIjtcblxuLy8gc3JjL2RvbS1ldmVudHMvcmVzaXplL3Jlc2l6ZUVsZW1lbnQudHNcbnZhciBvYnNlcnZlcjtcbnZhciByZXNpemVIYW5kbGVycyA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgV2Vha01hcCgpO1xudmFyIGhhbmRsZU9ic2VydmF0aW9uID0gKGVudHJpZXMpID0+IGVudHJpZXMuZm9yRWFjaCgoeyB0YXJnZXQsIGNvbnRlbnRSZWN0IH0pID0+IHtcbiAgcmV0dXJuIHJlc2l6ZUhhbmRsZXJzLmdldCh0YXJnZXQpPy5mb3JFYWNoKChoYW5kbGVyKSA9PiBoYW5kbGVyKGNvbnRlbnRSZWN0KSk7XG59KTtcbmZ1bmN0aW9uIHJlc2l6ZUVsZW1lbnQoaGFuZGxlciwgdGFyZ2V0KSB7XG4gIGlmICghb2JzZXJ2ZXIpIHtcbiAgICBpZiAodHlwZW9mIFJlc2l6ZU9ic2VydmVyICE9PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICBvYnNlcnZlciA9IG5ldyBSZXNpemVPYnNlcnZlcihoYW5kbGVPYnNlcnZhdGlvbik7XG4gICAgfVxuICB9XG4gIGxldCBlbGVtZW50SGFuZGxlcnMgPSByZXNpemVIYW5kbGVycy5nZXQodGFyZ2V0KTtcbiAgaWYgKCFlbGVtZW50SGFuZGxlcnMpIHtcbiAgICBlbGVtZW50SGFuZGxlcnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFNldCgpO1xuICAgIHJlc2l6ZUhhbmRsZXJzLnNldCh0YXJnZXQsIGVsZW1lbnRIYW5kbGVycyk7XG4gIH1cbiAgZWxlbWVudEhhbmRsZXJzLmFkZChoYW5kbGVyKTtcbiAgaWYgKG9ic2VydmVyKSB7XG4gICAgb2JzZXJ2ZXIub2JzZXJ2ZSh0YXJnZXQpO1xuICB9XG4gIHJldHVybiAoKSA9PiB7XG4gICAgY29uc3QgZWxlbWVudEhhbmRsZXJzMiA9IHJlc2l6ZUhhbmRsZXJzLmdldCh0YXJnZXQpO1xuICAgIGlmICghZWxlbWVudEhhbmRsZXJzMilcbiAgICAgIHJldHVybjtcbiAgICBlbGVtZW50SGFuZGxlcnMyLmRlbGV0ZShoYW5kbGVyKTtcbiAgICBpZiAoIWVsZW1lbnRIYW5kbGVyczIuc2l6ZSAmJiBvYnNlcnZlcikge1xuICAgICAgb2JzZXJ2ZXIudW5vYnNlcnZlKHRhcmdldCk7XG4gICAgfVxuICB9O1xufVxuXG4vLyBzcmMvZG9tLWV2ZW50cy9yZXNpemUvcmVzaXplV2luZG93LnRzXG52YXIgbGlzdGVuZXJzID0gLyogQF9fUFVSRV9fICovIG5ldyBTZXQoKTtcbnZhciBjbGVhbnVwV2luZG93UmVzaXplSGFuZGxlcjtcbnZhciBjcmVhdGVSZXNpemVIYW5kbGVyID0gKCkgPT4ge1xuICBjb25zdCBoYW5kbGVSZXNpemUgPSAoKSA9PiB7XG4gICAgbGlzdGVuZXJzLmZvckVhY2goXG4gICAgICAoY2FsbGJhY2spID0+IGNhbGxiYWNrKHtcbiAgICAgICAgd2lkdGg6IHdpbmRvdy5pbm5lcldpZHRoLFxuICAgICAgICBoZWlnaHQ6IHdpbmRvdy5pbm5lckhlaWdodFxuICAgICAgfSlcbiAgICApO1xuICB9O1xuICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcihcInJlc2l6ZVwiLCBoYW5kbGVSZXNpemUpO1xuICByZXR1cm4gKCkgPT4ge1xuICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKFwicmVzaXplXCIsIGhhbmRsZVJlc2l6ZSk7XG4gIH07XG59O1xudmFyIHJlc2l6ZVdpbmRvdyA9IChjYWxsYmFjaykgPT4ge1xuICBsaXN0ZW5lcnMuYWRkKGNhbGxiYWNrKTtcbiAgaWYgKCFjbGVhbnVwV2luZG93UmVzaXplSGFuZGxlcikge1xuICAgIGNsZWFudXBXaW5kb3dSZXNpemVIYW5kbGVyID0gY3JlYXRlUmVzaXplSGFuZGxlcigpO1xuICB9XG4gIHJldHVybiAoKSA9PiB7XG4gICAgbGlzdGVuZXJzLmRlbGV0ZShjYWxsYmFjayk7XG4gICAgaWYgKCFsaXN0ZW5lcnMuc2l6ZSAmJiBjbGVhbnVwV2luZG93UmVzaXplSGFuZGxlcikge1xuICAgICAgY2xlYW51cFdpbmRvd1Jlc2l6ZUhhbmRsZXIoKTtcbiAgICAgIGNsZWFudXBXaW5kb3dSZXNpemVIYW5kbGVyID0gdm9pZCAwO1xuICAgIH1cbiAgfTtcbn07XG5cbi8vIHNyYy9kb20tZXZlbnRzL3Jlc2l6ZS9pbmRleC50c1xudmFyIG9uUmVzaXplID0gKGNhbGxiYWNrLCB7IGNvbnRhaW5lciA9IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudCB9ID0ge30pID0+IHtcbiAgaWYgKGNvbnRhaW5lciA9PT0gZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50KSB7XG4gICAgcmV0dXJuIHJlc2l6ZVdpbmRvdyhjYWxsYmFjayk7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIHJlc2l6ZUVsZW1lbnQoY2FsbGJhY2ssIGNvbnRhaW5lcik7XG4gIH1cbn07XG5cbi8vIHNyYy9wcm9ncmVzcy50c1xudmFyIHByb2dyZXNzID0gKG1pbiwgbWF4LCB2YWx1ZSkgPT4gbWF4IC0gbWluID09PSAwID8gMSA6ICh2YWx1ZSAtIG1pbikgLyAobWF4IC0gbWluKTtcblxuLy8gc3JjL2RvbS1ldmVudHMvc2Nyb2xsL1Njcm9sbEhhbmRsZXIudHNcbnZhciBTQ1JPTExfS0VZUyA9IHtcbiAgeDoge1xuICAgIGxlbmd0aDogXCJXaWR0aFwiLFxuICAgIHBvc2l0aW9uOiBcIkxlZnRcIlxuICB9LFxuICB5OiB7XG4gICAgbGVuZ3RoOiBcIkhlaWdodFwiLFxuICAgIHBvc2l0aW9uOiBcIlRvcFwiXG4gIH1cbn07XG52YXIgU2Nyb2xsSGFuZGxlciA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IoY2FsbGJhY2ssIGNvbnRhaW5lcikge1xuICAgIHRoaXMuY3JlYXRlQXhpcyA9ICgpID0+ICh7XG4gICAgICBjdXJyZW50OiAwLFxuICAgICAgcHJvZ3Jlc3M6IDAsXG4gICAgICBzY3JvbGxMZW5ndGg6IDBcbiAgICB9KTtcbiAgICB0aGlzLnVwZGF0ZUF4aXMgPSAoYXhpc05hbWUpID0+IHtcbiAgICAgIGNvbnN0IGF4aXMgPSB0aGlzLmluZm9bYXhpc05hbWVdO1xuICAgICAgY29uc3QgeyBsZW5ndGgsIHBvc2l0aW9uIH0gPSBTQ1JPTExfS0VZU1theGlzTmFtZV07XG4gICAgICBheGlzLmN1cnJlbnQgPSB0aGlzLmNvbnRhaW5lcltgc2Nyb2xsJHtwb3NpdGlvbn1gXTtcbiAgICAgIGF4aXMuc2Nyb2xsTGVuZ3RoID0gdGhpcy5jb250YWluZXJbYHNjcm9sbCR7bGVuZ3RofWBdIC0gdGhpcy5jb250YWluZXJbYGNsaWVudCR7bGVuZ3RofWBdO1xuICAgICAgYXhpcy5wcm9ncmVzcyA9IHByb2dyZXNzKDAsIGF4aXMuc2Nyb2xsTGVuZ3RoLCBheGlzLmN1cnJlbnQpO1xuICAgIH07XG4gICAgdGhpcy51cGRhdGUgPSAoKSA9PiB7XG4gICAgICB0aGlzLnVwZGF0ZUF4aXMoXCJ4XCIpO1xuICAgICAgdGhpcy51cGRhdGVBeGlzKFwieVwiKTtcbiAgICB9O1xuICAgIHRoaXMuc2VuZEV2ZW50ID0gKCkgPT4ge1xuICAgICAgdGhpcy5jYWxsYmFjayh0aGlzLmluZm8pO1xuICAgIH07XG4gICAgdGhpcy5hZHZhbmNlID0gKCkgPT4ge1xuICAgICAgdGhpcy51cGRhdGUoKTtcbiAgICAgIHRoaXMuc2VuZEV2ZW50KCk7XG4gICAgfTtcbiAgICB0aGlzLmNhbGxiYWNrID0gY2FsbGJhY2s7XG4gICAgdGhpcy5jb250YWluZXIgPSBjb250YWluZXI7XG4gICAgdGhpcy5pbmZvID0ge1xuICAgICAgdGltZTogMCxcbiAgICAgIHg6IHRoaXMuY3JlYXRlQXhpcygpLFxuICAgICAgeTogdGhpcy5jcmVhdGVBeGlzKClcbiAgICB9O1xuICB9XG59O1xuXG4vLyBzcmMvZG9tLWV2ZW50cy9zY3JvbGwvaW5kZXgudHNcbnZhciBzY3JvbGxMaXN0ZW5lcnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFdlYWtNYXAoKTtcbnZhciByZXNpemVMaXN0ZW5lcnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFdlYWtNYXAoKTtcbnZhciBvblNjcm9sbEhhbmRsZXJzID0gLyogQF9fUFVSRV9fICovIG5ldyBXZWFrTWFwKCk7XG52YXIgZ2V0VGFyZ2V0ID0gKGNvbnRhaW5lcikgPT4gY29udGFpbmVyID09PSBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQgPyB3aW5kb3cgOiBjb250YWluZXI7XG52YXIgb25TY3JvbGwgPSAoY2FsbGJhY2ssIHsgY29udGFpbmVyID0gZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50IH0gPSB7fSkgPT4ge1xuICBsZXQgY29udGFpbmVySGFuZGxlcnMgPSBvblNjcm9sbEhhbmRsZXJzLmdldChjb250YWluZXIpO1xuICBpZiAoIWNvbnRhaW5lckhhbmRsZXJzKSB7XG4gICAgY29udGFpbmVySGFuZGxlcnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFNldCgpO1xuICAgIG9uU2Nyb2xsSGFuZGxlcnMuc2V0KGNvbnRhaW5lciwgY29udGFpbmVySGFuZGxlcnMpO1xuICB9XG4gIGNvbnN0IGNvbnRhaW5lckhhbmRsZXIgPSBuZXcgU2Nyb2xsSGFuZGxlcihjYWxsYmFjaywgY29udGFpbmVyKTtcbiAgY29udGFpbmVySGFuZGxlcnMuYWRkKGNvbnRhaW5lckhhbmRsZXIpO1xuICBpZiAoIXNjcm9sbExpc3RlbmVycy5oYXMoY29udGFpbmVyKSkge1xuICAgIGNvbnN0IGxpc3RlbmVyID0gKCkgPT4ge1xuICAgICAgY29udGFpbmVySGFuZGxlcnM/LmZvckVhY2goKGhhbmRsZXIpID0+IGhhbmRsZXIuYWR2YW5jZSgpKTtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH07XG4gICAgc2Nyb2xsTGlzdGVuZXJzLnNldChjb250YWluZXIsIGxpc3RlbmVyKTtcbiAgICBjb25zdCB0YXJnZXQgPSBnZXRUYXJnZXQoY29udGFpbmVyKTtcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcihcInJlc2l6ZVwiLCBsaXN0ZW5lciwgeyBwYXNzaXZlOiB0cnVlIH0pO1xuICAgIGlmIChjb250YWluZXIgIT09IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudCkge1xuICAgICAgcmVzaXplTGlzdGVuZXJzLnNldChjb250YWluZXIsIG9uUmVzaXplKGxpc3RlbmVyLCB7IGNvbnRhaW5lciB9KSk7XG4gICAgfVxuICAgIHRhcmdldC5hZGRFdmVudExpc3RlbmVyKFwic2Nyb2xsXCIsIGxpc3RlbmVyLCB7IHBhc3NpdmU6IHRydWUgfSk7XG4gIH1cbiAgY29uc3QgYW5pbWF0ZVNjcm9sbCA9IHNjcm9sbExpc3RlbmVycy5nZXQoY29udGFpbmVyKTtcbiAgcmFmMyhhbmltYXRlU2Nyb2xsKTtcbiAgcmV0dXJuICgpID0+IHtcbiAgICByYWYzLmNhbmNlbChhbmltYXRlU2Nyb2xsKTtcbiAgICBjb25zdCBjb250YWluZXJIYW5kbGVyczIgPSBvblNjcm9sbEhhbmRsZXJzLmdldChjb250YWluZXIpO1xuICAgIGlmICghY29udGFpbmVySGFuZGxlcnMyKVxuICAgICAgcmV0dXJuO1xuICAgIGNvbnRhaW5lckhhbmRsZXJzMi5kZWxldGUoY29udGFpbmVySGFuZGxlcik7XG4gICAgaWYgKGNvbnRhaW5lckhhbmRsZXJzMi5zaXplKVxuICAgICAgcmV0dXJuO1xuICAgIGNvbnN0IGxpc3RlbmVyID0gc2Nyb2xsTGlzdGVuZXJzLmdldChjb250YWluZXIpO1xuICAgIHNjcm9sbExpc3RlbmVycy5kZWxldGUoY29udGFpbmVyKTtcbiAgICBpZiAobGlzdGVuZXIpIHtcbiAgICAgIGdldFRhcmdldChjb250YWluZXIpLnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJzY3JvbGxcIiwgbGlzdGVuZXIpO1xuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJyZXNpemVcIiwgbGlzdGVuZXIpO1xuICAgICAgcmVzaXplTGlzdGVuZXJzLmdldChjb250YWluZXIpPy4oKTtcbiAgICB9XG4gIH07XG59O1xuXG4vLyBzcmMvaG9va3MvdXNlQ29uc3RhbnQudHNcbmltcG9ydCB7IHVzZVJlZiB9IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gdXNlQ29uc3RhbnQoaW5pdCkge1xuICBjb25zdCByZWYgPSB1c2VSZWYobnVsbCk7XG4gIGlmIChyZWYuY3VycmVudCA9PT0gbnVsbCkge1xuICAgIHJlZi5jdXJyZW50ID0gaW5pdCgpO1xuICB9XG4gIHJldHVybiByZWYuY3VycmVudDtcbn1cblxuLy8gc3JjL2hvb2tzL3VzZUZvcmNlVXBkYXRlLnRzXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xuXG4vLyBzcmMvaG9va3MvdXNlSXNNb3VudGVkLnRzXG5pbXBvcnQgeyB1c2VSZWYgYXMgdXNlUmVmMiB9IGZyb20gXCJyZWFjdFwiO1xuXG4vLyBzcmMvaG9va3MvdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdC50c1xuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VMYXlvdXRFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcbnZhciB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0ID0gaXNTU1IoKSA/IHVzZUVmZmVjdCA6IHVzZUxheW91dEVmZmVjdDtcblxuLy8gc3JjL2hvb2tzL3VzZUlzTW91bnRlZC50c1xudmFyIHVzZUlzTW91bnRlZCA9ICgpID0+IHtcbiAgY29uc3QgaXNNb3VudGVkID0gdXNlUmVmMihmYWxzZSk7XG4gIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgIGlzTW91bnRlZC5jdXJyZW50ID0gdHJ1ZTtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgaXNNb3VudGVkLmN1cnJlbnQgPSBmYWxzZTtcbiAgICB9O1xuICB9LCBbXSk7XG4gIHJldHVybiBpc01vdW50ZWQ7XG59O1xuXG4vLyBzcmMvaG9va3MvdXNlRm9yY2VVcGRhdGUudHNcbmZ1bmN0aW9uIHVzZUZvcmNlVXBkYXRlKCkge1xuICBjb25zdCB1cGRhdGUgPSB1c2VTdGF0ZSgpWzFdO1xuICBjb25zdCBpc01vdW50ZWQgPSB1c2VJc01vdW50ZWQoKTtcbiAgcmV0dXJuICgpID0+IHtcbiAgICBpZiAoaXNNb3VudGVkLmN1cnJlbnQpIHtcbiAgICAgIHVwZGF0ZShNYXRoLnJhbmRvbSgpKTtcbiAgICB9XG4gIH07XG59XG5cbi8vIHNyYy9ob29rcy91c2VNZW1vT25lLnRzXG5pbXBvcnQgeyB1c2VFZmZlY3QgYXMgdXNlRWZmZWN0MiwgdXNlUmVmIGFzIHVzZVJlZjMsIHVzZVN0YXRlIGFzIHVzZVN0YXRlMiB9IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gdXNlTWVtb09uZShnZXRSZXN1bHQsIGlucHV0cykge1xuICBjb25zdCBbaW5pdGlhbF0gPSB1c2VTdGF0ZTIoXG4gICAgKCkgPT4gKHtcbiAgICAgIGlucHV0cyxcbiAgICAgIHJlc3VsdDogZ2V0UmVzdWx0KClcbiAgICB9KVxuICApO1xuICBjb25zdCBjb21taXR0ZWQgPSB1c2VSZWYzKCk7XG4gIGNvbnN0IHByZXZDYWNoZSA9IGNvbW1pdHRlZC5jdXJyZW50O1xuICBsZXQgY2FjaGUgPSBwcmV2Q2FjaGU7XG4gIGlmIChjYWNoZSkge1xuICAgIGNvbnN0IHVzZUNhY2hlID0gQm9vbGVhbihcbiAgICAgIGlucHV0cyAmJiBjYWNoZS5pbnB1dHMgJiYgYXJlSW5wdXRzRXF1YWwoaW5wdXRzLCBjYWNoZS5pbnB1dHMpXG4gICAgKTtcbiAgICBpZiAoIXVzZUNhY2hlKSB7XG4gICAgICBjYWNoZSA9IHtcbiAgICAgICAgaW5wdXRzLFxuICAgICAgICByZXN1bHQ6IGdldFJlc3VsdCgpXG4gICAgICB9O1xuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBjYWNoZSA9IGluaXRpYWw7XG4gIH1cbiAgdXNlRWZmZWN0MigoKSA9PiB7XG4gICAgY29tbWl0dGVkLmN1cnJlbnQgPSBjYWNoZTtcbiAgICBpZiAocHJldkNhY2hlID09IGluaXRpYWwpIHtcbiAgICAgIGluaXRpYWwuaW5wdXRzID0gaW5pdGlhbC5yZXN1bHQgPSB2b2lkIDA7XG4gICAgfVxuICB9LCBbY2FjaGVdKTtcbiAgcmV0dXJuIGNhY2hlLnJlc3VsdDtcbn1cbmZ1bmN0aW9uIGFyZUlucHV0c0VxdWFsKG5leHQsIHByZXYpIHtcbiAgaWYgKG5leHQubGVuZ3RoICE9PSBwcmV2Lmxlbmd0aCkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICBmb3IgKGxldCBpID0gMDsgaSA8IG5leHQubGVuZ3RoOyBpKyspIHtcbiAgICBpZiAobmV4dFtpXSAhPT0gcHJldltpXSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfVxuICByZXR1cm4gdHJ1ZTtcbn1cblxuLy8gc3JjL2hvb2tzL3VzZU9uY2UudHNcbmltcG9ydCB7IHVzZUVmZmVjdCBhcyB1c2VFZmZlY3QzIH0gZnJvbSBcInJlYWN0XCI7XG52YXIgdXNlT25jZSA9IChlZmZlY3QpID0+IHVzZUVmZmVjdDMoZWZmZWN0LCBlbXB0eURlcHMpO1xudmFyIGVtcHR5RGVwcyA9IFtdO1xuXG4vLyBzcmMvaG9va3MvdXNlUHJldi50c1xuaW1wb3J0IHsgdXNlRWZmZWN0IGFzIHVzZUVmZmVjdDQsIHVzZVJlZiBhcyB1c2VSZWY0IH0gZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VQcmV2KHZhbHVlKSB7XG4gIGNvbnN0IHByZXZSZWYgPSB1c2VSZWY0KCk7XG4gIHVzZUVmZmVjdDQoKCkgPT4ge1xuICAgIHByZXZSZWYuY3VycmVudCA9IHZhbHVlO1xuICB9KTtcbiAgcmV0dXJuIHByZXZSZWYuY3VycmVudDtcbn1cblxuLy8gc3JjL2hvb2tzL3VzZVJlZHVjZWRNb3Rpb24udHNcbmltcG9ydCB7IHVzZVN0YXRlIGFzIHVzZVN0YXRlMyB9IGZyb20gXCJyZWFjdFwiO1xudmFyIHVzZVJlZHVjZWRNb3Rpb24gPSAoKSA9PiB7XG4gIGNvbnN0IFtyZWR1Y2VkTW90aW9uLCBzZXRSZWR1Y2VkTW90aW9uXSA9IHVzZVN0YXRlMyhudWxsKTtcbiAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgbXFsID0gd2luZG93Lm1hdGNoTWVkaWEoXCIocHJlZmVycy1yZWR1Y2VkLW1vdGlvbilcIik7XG4gICAgY29uc3QgaGFuZGxlTWVkaWFDaGFuZ2UgPSAoZSkgPT4ge1xuICAgICAgc2V0UmVkdWNlZE1vdGlvbihlLm1hdGNoZXMpO1xuICAgICAgYXNzaWduKHtcbiAgICAgICAgc2tpcEFuaW1hdGlvbjogZS5tYXRjaGVzXG4gICAgICB9KTtcbiAgICB9O1xuICAgIGhhbmRsZU1lZGlhQ2hhbmdlKG1xbCk7XG4gICAgaWYgKG1xbC5hZGRFdmVudExpc3RlbmVyKSB7XG4gICAgICBtcWwuYWRkRXZlbnRMaXN0ZW5lcihcImNoYW5nZVwiLCBoYW5kbGVNZWRpYUNoYW5nZSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIG1xbC5hZGRMaXN0ZW5lcihoYW5kbGVNZWRpYUNoYW5nZSk7XG4gICAgfVxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBpZiAobXFsLnJlbW92ZUV2ZW50TGlzdGVuZXIpIHtcbiAgICAgICAgbXFsLnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJjaGFuZ2VcIiwgaGFuZGxlTWVkaWFDaGFuZ2UpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgbXFsLnJlbW92ZUxpc3RlbmVyKGhhbmRsZU1lZGlhQ2hhbmdlKTtcbiAgICAgIH1cbiAgICB9O1xuICB9LCBbXSk7XG4gIHJldHVybiByZWR1Y2VkTW90aW9uO1xufTtcblxuLy8gc3JjL2luZGV4LnRzXG5pbXBvcnQgeyByYWYgYXMgcmFmNCB9IGZyb20gXCJAcmVhY3Qtc3ByaW5nL3JhZnpcIjtcbmV4cG9ydCB7XG4gIEZsdWlkVmFsdWUsXG4gIGdsb2JhbHNfZXhwb3J0cyBhcyBHbG9iYWxzLFxuICBhZGRGbHVpZE9ic2VydmVyLFxuICBjYWxsRmx1aWRPYnNlcnZlcixcbiAgY2FsbEZsdWlkT2JzZXJ2ZXJzLFxuICBjbGFtcCxcbiAgY29sb3JUb1JnYmEsXG4gIGNvbG9yczIgYXMgY29sb3JzLFxuICBjcmVhdGVJbnRlcnBvbGF0b3IsXG4gIGNyZWF0ZVN0cmluZ0ludGVycG9sYXRvcjIgYXMgY3JlYXRlU3RyaW5nSW50ZXJwb2xhdG9yLFxuICBkZWZpbmVIaWRkZW4sXG4gIGRlcHJlY2F0ZURpcmVjdENhbGwsXG4gIGRlcHJlY2F0ZUludGVycG9sYXRlLFxuICBlYWNoLFxuICBlYWNoUHJvcCxcbiAgZWFzaW5ncyxcbiAgZmx1c2gsXG4gIGZsdXNoQ2FsbHMsXG4gIGZyYW1lTG9vcCxcbiAgZ2V0Rmx1aWRPYnNlcnZlcnMsXG4gIGdldEZsdWlkVmFsdWUsXG4gIGhhc0ZsdWlkVmFsdWUsXG4gIGhleDMsXG4gIGhleDQsXG4gIGhleDYsXG4gIGhleDgsXG4gIGhzbCxcbiAgaHNsYSxcbiAgaXMsXG4gIGlzQW5pbWF0ZWRTdHJpbmcsXG4gIGlzRXF1YWwsXG4gIGlzU1NSLFxuICBub29wLFxuICBvblJlc2l6ZSxcbiAgb25TY3JvbGwsXG4gIG9uY2UsXG4gIHByZWZpeCxcbiAgcmFmNCBhcyByYWYsXG4gIHJlbW92ZUZsdWlkT2JzZXJ2ZXIsXG4gIHJnYixcbiAgcmdiYSxcbiAgc2V0Rmx1aWRHZXR0ZXIsXG4gIHRvQXJyYXksXG4gIHVzZUNvbnN0YW50LFxuICB1c2VGb3JjZVVwZGF0ZSxcbiAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCxcbiAgdXNlTWVtb09uZSxcbiAgdXNlT25jZSxcbiAgdXNlUHJldixcbiAgdXNlUmVkdWNlZE1vdGlvblxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlYWN0LXNwcmluZ19zaGFyZWQubW9kZXJuLm1qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-spring/types/dist/react-spring_types.modern.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/@react-spring/types/dist/react-spring_types.modern.mjs ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Any: () => (/* binding */ Any)\n/* harmony export */ });\n// src/utils.ts\nvar Any = class {\n};\n\n//# sourceMappingURL=react-spring_types.modern.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlYWN0LXNwcmluZy90eXBlcy9kaXN0L3JlYWN0LXNwcmluZ190eXBlcy5tb2Rlcm4ubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGVudGFiZWxsLy4vbm9kZV9tb2R1bGVzL0ByZWFjdC1zcHJpbmcvdHlwZXMvZGlzdC9yZWFjdC1zcHJpbmdfdHlwZXMubW9kZXJuLm1qcz80NWRhIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy91dGlscy50c1xudmFyIEFueSA9IGNsYXNzIHtcbn07XG5leHBvcnQge1xuICBBbnlcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZWFjdC1zcHJpbmdfdHlwZXMubW9kZXJuLm1qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-spring/types/dist/react-spring_types.modern.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-spring/web/dist/react-spring_web.modern.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/@react-spring/web/dist/react-spring_web.modern.mjs ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Any: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.Any),\n/* harmony export */   BailSignal: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.BailSignal),\n/* harmony export */   Controller: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.Controller),\n/* harmony export */   FrameValue: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.FrameValue),\n/* harmony export */   Globals: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.Globals),\n/* harmony export */   Interpolation: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.Interpolation),\n/* harmony export */   Spring: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.Spring),\n/* harmony export */   SpringContext: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.SpringContext),\n/* harmony export */   SpringRef: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.SpringRef),\n/* harmony export */   SpringValue: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.SpringValue),\n/* harmony export */   Trail: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.Trail),\n/* harmony export */   Transition: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.Transition),\n/* harmony export */   a: () => (/* binding */ animated),\n/* harmony export */   animated: () => (/* binding */ animated),\n/* harmony export */   config: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.config),\n/* harmony export */   createInterpolator: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.createInterpolator),\n/* harmony export */   easings: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.easings),\n/* harmony export */   inferTo: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.inferTo),\n/* harmony export */   interpolate: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.interpolate),\n/* harmony export */   to: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.to),\n/* harmony export */   update: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.update),\n/* harmony export */   useChain: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useChain),\n/* harmony export */   useInView: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useInView),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect),\n/* harmony export */   useReducedMotion: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useReducedMotion),\n/* harmony export */   useResize: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useResize),\n/* harmony export */   useScroll: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useScroll),\n/* harmony export */   useSpring: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useSpring),\n/* harmony export */   useSpringRef: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useSpringRef),\n/* harmony export */   useSpringValue: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useSpringValue),\n/* harmony export */   useSprings: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useSprings),\n/* harmony export */   useTrail: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useTrail),\n/* harmony export */   useTransition: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useTransition)\n/* harmony export */ });\n/* harmony import */ var _react_spring_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-spring/core */ \"(ssr)/./node_modules/@react-spring/core/dist/react-spring_core.modern.mjs\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _react_spring_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-spring/shared */ \"(ssr)/./node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs\");\n/* harmony import */ var _react_spring_animated__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-spring/animated */ \"(ssr)/./node_modules/@react-spring/animated/dist/react-spring_animated.modern.mjs\");\n// src/index.ts\n\n\n\n\n\n// src/applyAnimatedValues.ts\nvar isCustomPropRE = /^--/;\nfunction dangerousStyleValue(name, value) {\n  if (value == null || typeof value === \"boolean\" || value === \"\")\n    return \"\";\n  if (typeof value === \"number\" && value !== 0 && !isCustomPropRE.test(name) && !(isUnitlessNumber.hasOwnProperty(name) && isUnitlessNumber[name]))\n    return value + \"px\";\n  return (\"\" + value).trim();\n}\nvar attributeCache = {};\nfunction applyAnimatedValues(instance, props) {\n  if (!instance.nodeType || !instance.setAttribute) {\n    return false;\n  }\n  const isFilterElement = instance.nodeName === \"filter\" || instance.parentNode && instance.parentNode.nodeName === \"filter\";\n  const {\n    className,\n    style,\n    children,\n    scrollTop,\n    scrollLeft,\n    viewBox,\n    ...attributes\n  } = props;\n  const values = Object.values(attributes);\n  const names = Object.keys(attributes).map(\n    (name) => isFilterElement || instance.hasAttribute(name) ? name : attributeCache[name] || (attributeCache[name] = name.replace(\n      /([A-Z])/g,\n      // Attributes are written in dash case\n      (n) => \"-\" + n.toLowerCase()\n    ))\n  );\n  if (children !== void 0) {\n    instance.textContent = children;\n  }\n  for (const name in style) {\n    if (style.hasOwnProperty(name)) {\n      const value = dangerousStyleValue(name, style[name]);\n      if (isCustomPropRE.test(name)) {\n        instance.style.setProperty(name, value);\n      } else {\n        instance.style[name] = value;\n      }\n    }\n  }\n  names.forEach((name, i) => {\n    instance.setAttribute(name, values[i]);\n  });\n  if (className !== void 0) {\n    instance.className = className;\n  }\n  if (scrollTop !== void 0) {\n    instance.scrollTop = scrollTop;\n  }\n  if (scrollLeft !== void 0) {\n    instance.scrollLeft = scrollLeft;\n  }\n  if (viewBox !== void 0) {\n    instance.setAttribute(\"viewBox\", viewBox);\n  }\n}\nvar isUnitlessNumber = {\n  animationIterationCount: true,\n  borderImageOutset: true,\n  borderImageSlice: true,\n  borderImageWidth: true,\n  boxFlex: true,\n  boxFlexGroup: true,\n  boxOrdinalGroup: true,\n  columnCount: true,\n  columns: true,\n  flex: true,\n  flexGrow: true,\n  flexPositive: true,\n  flexShrink: true,\n  flexNegative: true,\n  flexOrder: true,\n  gridRow: true,\n  gridRowEnd: true,\n  gridRowSpan: true,\n  gridRowStart: true,\n  gridColumn: true,\n  gridColumnEnd: true,\n  gridColumnSpan: true,\n  gridColumnStart: true,\n  fontWeight: true,\n  lineClamp: true,\n  lineHeight: true,\n  opacity: true,\n  order: true,\n  orphans: true,\n  tabSize: true,\n  widows: true,\n  zIndex: true,\n  zoom: true,\n  // SVG-related properties\n  fillOpacity: true,\n  floodOpacity: true,\n  stopOpacity: true,\n  strokeDasharray: true,\n  strokeDashoffset: true,\n  strokeMiterlimit: true,\n  strokeOpacity: true,\n  strokeWidth: true\n};\nvar prefixKey = (prefix, key) => prefix + key.charAt(0).toUpperCase() + key.substring(1);\nvar prefixes = [\"Webkit\", \"Ms\", \"Moz\", \"O\"];\nisUnitlessNumber = Object.keys(isUnitlessNumber).reduce((acc, prop) => {\n  prefixes.forEach((prefix) => acc[prefixKey(prefix, prop)] = acc[prop]);\n  return acc;\n}, isUnitlessNumber);\n\n// src/AnimatedStyle.ts\n\n\nvar domTransforms = /^(matrix|translate|scale|rotate|skew)/;\nvar pxTransforms = /^(translate)/;\nvar degTransforms = /^(rotate|skew)/;\nvar addUnit = (value, unit) => _react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.is.num(value) && value !== 0 ? value + unit : value;\nvar isValueIdentity = (value, id) => _react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.is.arr(value) ? value.every((v) => isValueIdentity(v, id)) : _react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.is.num(value) ? value === id : parseFloat(value) === id;\nvar AnimatedStyle = class extends _react_spring_animated__WEBPACK_IMPORTED_MODULE_3__.AnimatedObject {\n  constructor({ x, y, z, ...style }) {\n    const inputs = [];\n    const transforms = [];\n    if (x || y || z) {\n      inputs.push([x || 0, y || 0, z || 0]);\n      transforms.push((xyz) => [\n        `translate3d(${xyz.map((v) => addUnit(v, \"px\")).join(\",\")})`,\n        // prettier-ignore\n        isValueIdentity(xyz, 0)\n      ]);\n    }\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.eachProp)(style, (value, key) => {\n      if (key === \"transform\") {\n        inputs.push([value || \"\"]);\n        transforms.push((transform) => [transform, transform === \"\"]);\n      } else if (domTransforms.test(key)) {\n        delete style[key];\n        if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.is.und(value))\n          return;\n        const unit = pxTransforms.test(key) ? \"px\" : degTransforms.test(key) ? \"deg\" : \"\";\n        inputs.push((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.toArray)(value));\n        transforms.push(\n          key === \"rotate3d\" ? ([x2, y2, z2, deg]) => [\n            `rotate3d(${x2},${y2},${z2},${addUnit(deg, unit)})`,\n            isValueIdentity(deg, 0)\n          ] : (input) => [\n            `${key}(${input.map((v) => addUnit(v, unit)).join(\",\")})`,\n            isValueIdentity(input, key.startsWith(\"scale\") ? 1 : 0)\n          ]\n        );\n      }\n    });\n    if (inputs.length) {\n      style.transform = new FluidTransform(inputs, transforms);\n    }\n    super(style);\n  }\n};\nvar FluidTransform = class extends _react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.FluidValue {\n  constructor(inputs, transforms) {\n    super();\n    this.inputs = inputs;\n    this.transforms = transforms;\n    this._value = null;\n  }\n  get() {\n    return this._value || (this._value = this._get());\n  }\n  _get() {\n    let transform = \"\";\n    let identity = true;\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.each)(this.inputs, (input, i) => {\n      const arg1 = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.getFluidValue)(input[0]);\n      const [t, id] = this.transforms[i](\n        _react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.is.arr(arg1) ? arg1 : input.map(_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.getFluidValue)\n      );\n      transform += \" \" + t;\n      identity = identity && id;\n    });\n    return identity ? \"none\" : transform;\n  }\n  // Start observing our inputs once we have an observer.\n  observerAdded(count) {\n    if (count == 1)\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.each)(\n        this.inputs,\n        (input) => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.each)(\n          input,\n          (value) => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.hasFluidValue)(value) && (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.addFluidObserver)(value, this)\n        )\n      );\n  }\n  // Stop observing our inputs once we have no observers.\n  observerRemoved(count) {\n    if (count == 0)\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.each)(\n        this.inputs,\n        (input) => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.each)(\n          input,\n          (value) => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.hasFluidValue)(value) && (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.removeFluidObserver)(value, this)\n        )\n      );\n  }\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      this._value = null;\n    }\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.callFluidObservers)(this, event);\n  }\n};\n\n// src/primitives.ts\nvar primitives = [\n  \"a\",\n  \"abbr\",\n  \"address\",\n  \"area\",\n  \"article\",\n  \"aside\",\n  \"audio\",\n  \"b\",\n  \"base\",\n  \"bdi\",\n  \"bdo\",\n  \"big\",\n  \"blockquote\",\n  \"body\",\n  \"br\",\n  \"button\",\n  \"canvas\",\n  \"caption\",\n  \"cite\",\n  \"code\",\n  \"col\",\n  \"colgroup\",\n  \"data\",\n  \"datalist\",\n  \"dd\",\n  \"del\",\n  \"details\",\n  \"dfn\",\n  \"dialog\",\n  \"div\",\n  \"dl\",\n  \"dt\",\n  \"em\",\n  \"embed\",\n  \"fieldset\",\n  \"figcaption\",\n  \"figure\",\n  \"footer\",\n  \"form\",\n  \"h1\",\n  \"h2\",\n  \"h3\",\n  \"h4\",\n  \"h5\",\n  \"h6\",\n  \"head\",\n  \"header\",\n  \"hgroup\",\n  \"hr\",\n  \"html\",\n  \"i\",\n  \"iframe\",\n  \"img\",\n  \"input\",\n  \"ins\",\n  \"kbd\",\n  \"keygen\",\n  \"label\",\n  \"legend\",\n  \"li\",\n  \"link\",\n  \"main\",\n  \"map\",\n  \"mark\",\n  \"menu\",\n  \"menuitem\",\n  \"meta\",\n  \"meter\",\n  \"nav\",\n  \"noscript\",\n  \"object\",\n  \"ol\",\n  \"optgroup\",\n  \"option\",\n  \"output\",\n  \"p\",\n  \"param\",\n  \"picture\",\n  \"pre\",\n  \"progress\",\n  \"q\",\n  \"rp\",\n  \"rt\",\n  \"ruby\",\n  \"s\",\n  \"samp\",\n  \"script\",\n  \"section\",\n  \"select\",\n  \"small\",\n  \"source\",\n  \"span\",\n  \"strong\",\n  \"style\",\n  \"sub\",\n  \"summary\",\n  \"sup\",\n  \"table\",\n  \"tbody\",\n  \"td\",\n  \"textarea\",\n  \"tfoot\",\n  \"th\",\n  \"thead\",\n  \"time\",\n  \"title\",\n  \"tr\",\n  \"track\",\n  \"u\",\n  \"ul\",\n  \"var\",\n  \"video\",\n  \"wbr\",\n  // SVG\n  \"circle\",\n  \"clipPath\",\n  \"defs\",\n  \"ellipse\",\n  \"foreignObject\",\n  \"g\",\n  \"image\",\n  \"line\",\n  \"linearGradient\",\n  \"mask\",\n  \"path\",\n  \"pattern\",\n  \"polygon\",\n  \"polyline\",\n  \"radialGradient\",\n  \"rect\",\n  \"stop\",\n  \"svg\",\n  \"text\",\n  \"tspan\"\n];\n\n// src/index.ts\n\n_react_spring_core__WEBPACK_IMPORTED_MODULE_0__.Globals.assign({\n  batchedUpdates: react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates,\n  createStringInterpolator: _react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.createStringInterpolator,\n  colors: _react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.colors\n});\nvar host = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_3__.createHost)(primitives, {\n  applyAnimatedValues,\n  createAnimatedStyle: (style) => new AnimatedStyle(style),\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  getComponentProps: ({ scrollTop, scrollLeft, ...props }) => props\n});\nvar animated = host.animated;\n\n//# sourceMappingURL=react-spring_web.modern.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-spring/web/dist/react-spring_web.modern.mjs\n");

/***/ })

};
;