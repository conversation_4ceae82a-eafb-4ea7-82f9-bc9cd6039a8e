"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateUpdateCategory = exports.validateUpdateCategoryVersion = exports.validateCreateCategory = exports.updateCategorySchema = exports.updateCategoryVersionSchema = exports.createCategorySchema = void 0;
const joi_1 = __importDefault(require("joi"));
const constants_1 = require("@/utils/helpers/constants");
const categoryVersionSchema = joi_1.default.object({
    language: joi_1.default.string()
        .valid(...Object.values(constants_1.Language))
        .required(),
    name: joi_1.default.string().min(2).max(100).required().messages({
        'string.min': 'Category name must be at least 2 characters long',
        'string.max': 'Category name cannot exceed 100 characters',
        'any.required': 'Category name is required',
    }),
    url: joi_1.default.string()
        .min(2)
        .max(150)
        .pattern(/^[a-z0-9-]+$/)
        .optional()
        .messages({
        'string.pattern.base': 'URL must contain only lowercase letters, numbers, and hyphens',
        'string.min': 'URL must be at least 2 characters long',
        'string.max': 'URL cannot exceed 150 characters',
    }),
    description: joi_1.default.string().max(500).optional().messages({
        'string.max': 'Description cannot exceed 500 characters',
    }),
    image: joi_1.default.string().optional().allow(null),
    metaTitle: joi_1.default.string().max(60).optional().allow('').messages({
        'string.max': 'Meta title cannot exceed 60 characters',
    }),
    metaDescription: joi_1.default.string().max(160).optional().allow('').messages({
        'string.max': 'Meta description cannot exceed 160 characters',
    }),
    articles: joi_1.default.array()
        .items(joi_1.default.string().pattern(/^[0-9a-fA-F]{24}$/))
        .optional()
        .messages({
        'string.pattern.base': 'Article ID must be a valid MongoDB ObjectId',
    }),
});
exports.createCategorySchema = joi_1.default.object({
    versions: joi_1.default.object()
        .pattern(joi_1.default.string().valid(...Object.values(constants_1.Language)), categoryVersionSchema)
        .min(1)
        .required()
        .messages({
        'object.min': 'At least one language version is required',
        'any.required': 'Category versions are required',
    }),
    robotsMeta: joi_1.default.string()
        .valid(...Object.values(constants_1.robotsMeta))
        .optional()
        .default('index'),
    categoryType: joi_1.default.string()
        .valid(...Object.values(constants_1.CategoryType))
        .optional()
        .default('article'),
});
exports.updateCategoryVersionSchema = joi_1.default.object({
    language: joi_1.default.string()
        .valid(...Object.values(constants_1.Language))
        .required(),
    name: joi_1.default.string().min(2).max(100).optional().messages({
        'string.min': 'Category name must be at least 2 characters long',
        'string.max': 'Category name cannot exceed 100 characters',
    }),
    url: joi_1.default.string()
        .min(2)
        .max(150)
        .pattern(/^[a-z0-9-]+$/)
        .optional()
        .messages({
        'string.pattern.base': 'URL must contain only lowercase letters, numbers, and hyphens',
        'string.min': 'URL must be at least 2 characters long',
        'string.max': 'URL cannot exceed 150 characters',
    }),
    description: joi_1.default.string().max(500).optional().messages({
        'string.max': 'Description cannot exceed 500 characters',
    }),
    image: joi_1.default.string().optional().allow(null),
    metaTitle: joi_1.default.string().max(60).optional().allow('').messages({
        'string.max': 'Meta title cannot exceed 60 characters',
    }),
    metaDescription: joi_1.default.string().max(160).optional().allow('').messages({
        'string.max': 'Meta description cannot exceed 160 characters',
    }),
    articles: joi_1.default.array()
        .items(joi_1.default.string().pattern(/^[0-9a-fA-F]{24}$/))
        .optional()
        .messages({
        'string.pattern.base': 'Article ID must be a valid MongoDB ObjectId',
    }),
});
exports.updateCategorySchema = joi_1.default.object({
    robotsMeta: joi_1.default.string()
        .valid(...Object.values(constants_1.robotsMeta))
        .optional(),
    categoryType: joi_1.default.string()
        .valid(...Object.values(constants_1.CategoryType))
        .optional(),
})
    .min(1)
    .messages({
    'object.min': 'At least one field must be provided for update',
});
const validateCreateCategory = (req, res, next) => {
    const { error } = exports.createCategorySchema.validate(req.body, { abortEarly: false });
    if (error) {
        const errors = error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message,
        }));
        return res.status(400).json({
            message: 'Validation failed',
            errors,
        });
    }
    next();
};
exports.validateCreateCategory = validateCreateCategory;
const validateUpdateCategoryVersion = (req, res, next) => {
    const { error } = exports.updateCategoryVersionSchema.validate(req.body, { abortEarly: false });
    if (error) {
        const errors = error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message,
        }));
        return res.status(400).json({
            message: 'Validation failed',
            errors,
        });
    }
    next();
};
exports.validateUpdateCategoryVersion = validateUpdateCategoryVersion;
const validateUpdateCategory = (req, res, next) => {
    const { error } = exports.updateCategorySchema.validate(req.body, { abortEarly: false });
    if (error) {
        const errors = error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message,
        }));
        return res.status(400).json({
            message: 'Validation failed',
            errors,
        });
    }
    next();
};
exports.validateUpdateCategory = validateUpdateCategory;
//# sourceMappingURL=article.category.validation.js.map