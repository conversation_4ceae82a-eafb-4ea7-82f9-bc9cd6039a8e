"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_fr_validations_json";
exports.ids = ["_ssr_src_locales_fr_validations_json"];
exports.modules = {

/***/ "(ssr)/./src/locales/fr/validations.json":
/*!*****************************************!*\
  !*** ./src/locales/fr/validations.json ***!
  \*****************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"invalidEmail":"Email invalide","invalidDate":"Date invalide","emptyField":"Veuillez remplir ce champ obligatoire !","endDate":"La date de fin doit être après la date de début","minDate":"La date de naissance doit être après 1950","maxDate":"La date doit être avant 2005","minLength":"Le champ doit comporter au moins 3 caractères","maxLength":"Le champ doit comporter au plus 20 caractères","required":"Ce champ est requis !","invalidPassword":"Le mot de passe doit comporter au moins une lettre majuscule, une lettre minuscule, un chiffre et un caractère spécial","passwordMatch":"Les mots de passe doivent correspondre","minOne":"Au moins une compétence est requise","minNationality":"Au moins une nationalité est requise","minRoles":"Au moins un role est requis","phoneFormat":"Format de numéro de téléphone invalide","companyEmailRequired":"Veuillez utiliser votre adresse e-mail professionnelle","titleRequired":"Le titre est requis","titleMinLength":"Le titre doit comporter au moins 3 caractères","titleMaxLength":"Le titre ne doit pas dépasser 200 caractères","urlRequired":"L\'URL est requise","urlInvalidFormat":"L\'URL ne doit contenir que des lettres minuscules, des chiffres et des tirets","urlMinLength":"L\'URL doit comporter au moins 3 caractères","urlMaxLength":"L\'URL ne doit pas dépasser 100 caractères","metaTitleRequired":"Le méta-titre est requis","metaTitleMinLength":"Le méta-titre doit comporter au moins 10 caractères","metaTitleMaxLength":"Le méta-titre ne doit pas dépasser 60 caractères","metaDescriptionRequired":"La méta-description est requise","metaDescriptionMinLength":"La méta-description doit comporter au moins 50 caractères","metaDescriptionMaxLength":"La méta-description ne doit pas dépasser 160 caractères","descriptionRequired":"La description est requise","descriptionMinLength":"La description doit comporter au moins 20 caractères","descriptionMaxLength":"La description ne doit pas dépasser 500 caractères","contentRequired":"Le contenu est requis","contentMinLength":"Le contenu doit comporter au moins 100 caractères","contentNotEmpty":"Le contenu ne peut pas être vide ou contenir uniquement des balises HTML","altRequired":"Le texte alternatif est requis","altMinLength":"Le texte alternatif doit comporter au moins 5 caractères","altMaxLength":"Le texte alternatif ne doit pas dépasser 125 caractères","visibilityRequired":"La visibilité est requise","visibilityInvalid":"Option de visibilité invalide","publishDateRequired":"La date de publication est requise pour les articles publics","categoryRequired":"Au moins une catégorie est requise","categoryMaxLimit":"Maximum 5 catégories autorisées","keywordsRequired":"Au moins un mot-clé est requis","keywordsMaxLimit":"Maximum 10 mots-clés autorisés","highlightsMaxLimit":"Maximum 5 points forts autorisés","canonicalInvalidUrl":"L\'URL canonique doit être une URL valide","faqTitleMaxLength":"Le titre FAQ ne doit pas dépasser 100 caractères","faqQuestionRequired":"La question FAQ est requise","faqQuestionMinLength":"La question FAQ doit comporter au moins 10 caractères","faqQuestionMaxLength":"La question FAQ ne doit pas dépasser 200 caractères","faqAnswerRequired":"La réponse FAQ est requise","faqAnswerMinLength":"La réponse FAQ doit comporter au moins 20 caractères","faqAnswerMaxLength":"La réponse FAQ ne doit pas dépasser 1000 caractères","faqMaxLimit":"Maximum 10 éléments FAQ autorisés","ctaBannerLinkInvalidUrl":"Le lien de la bannière CTA doit être une URL valide","robotsMetaRequired":"Les méta-robots sont requis","robotsMetaInvalid":"Option de méta-robots invalide","tagsMaxLimit":"Maximum 10 étiquettes autorisées","selectAtLeastOneLanguage":"Veuillez sélectionner au moins une langue","imageRequired":"L\'image est requise","ctaBannerIncomplete":"La bannière CTA nécessite à la fois une image et un lien","pleaseFixErrors":"Veuillez corriger les erreurs de validation avant de soumettre","noValidVersions":"Aucune version linguistique valide trouvée"}');

/***/ })

};
;