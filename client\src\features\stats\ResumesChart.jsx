"use client";
import { useTranslation } from "react-i18next";
import { useTheme, useMediaQuery, Grid } from "@mui/material";

import { Industry } from "../../utils/constants";
import { useGetCategories } from "../blog/hooks/blog.hook";
import CommentByCategory from "./charts/CommentByCategory";
import UsersActivities from "./charts/UsersActivities";
import ApplicationsByStatus from "./charts/ApplicationsByStatus";
import OpportunititesType from "./charts/OpportunititesType";
import PlateformActivities from "./charts/PlateformActivities";
import ArticlesByVisibility from "./charts/ArticlesByVisibility";
import Loading from "@/components/loading/Loading";

export function valueFormatter(value) {
  return `${value}`;
}

export default function ResumesChart({ language = "en " }) {
  const getCategoriesLangEN = useGetCategories("en");

  const transformedCategories =
    getCategoriesLangEN?.data?.categories?.map((category) => ({
      id: category?.versions?.en?.id || category?._id,
      name: category?.versions?.en?.name || category?.name,
    })) || [];

  const isLoadingCategories = getCategoriesLangEN.isLoading;

  const { t } = useTranslation();
  const theme = useTheme();

  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery(theme.breakpoints.down("md"));

  const chartSettings = {
    width: isMobile ? 290 : isTablet ? 500 : 580,
    height: 250,
    layout: "vertical",
  };

  if (isLoadingCategories) {
    return (
      <>
        <p className="heading-h2 semi-bold">{t("menu:statistics")}</p>
        <div id="stats" className="div-wrapper">
          <Loading />
        </div>
      </>
    );
  }

  return (
    <>
      <p className="heading-h2 semi-bold">{t("menu:statistics")}</p>
      <div id="stats" className="div-wrapper">
        <Grid container spacing={2}>
          <Grid item xs={12} sm={12} md={3}>
            <CommentByCategory
              transformedCategories={transformedCategories}
              t={t}
            />
          </Grid>
          <Grid item xs={12} sm={12} md={6}>
            <UsersActivities chartSettings={chartSettings} t={t} />
          </Grid>
          <Grid item xs={12} sm={12} md={3}>
            <ApplicationsByStatus t={t} />
          </Grid>
          <Grid item xs={12} sm={12} md={3}>
            <OpportunititesType Industry={Industry} t={t} language={language} />
          </Grid>
          <Grid item xs={12} sm={12} md={6}>
            <PlateformActivities chartSettings={chartSettings} t={t} />
          </Grid>
          <Grid item xs={12} sm={12} md={3}>
            <ArticlesByVisibility t={t} />
          </Grid>
        </Grid>
      </div>
    </>
  );
}
