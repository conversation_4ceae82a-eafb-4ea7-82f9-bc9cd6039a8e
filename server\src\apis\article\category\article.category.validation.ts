import Joi from 'joi';
import { Language, CategoryType, robotsMeta } from '@/utils/helpers/constants';

const categoryVersionSchema = Joi.object({
    language: Joi.string()
        .valid(...Object.values(Language))
        .required(),
    name: Joi.string().min(2).max(100).required().messages({
        'string.min': 'Category name must be at least 2 characters long',
        'string.max': 'Category name cannot exceed 100 characters',
        'any.required': 'Category name is required',
    }),
    url: Joi.string()
        .min(2)
        .max(150)
        .pattern(/^[a-z0-9-]+$/)
        .optional()
        .messages({
            'string.pattern.base': 'URL must contain only lowercase letters, numbers, and hyphens',
            'string.min': 'URL must be at least 2 characters long',
            'string.max': 'URL cannot exceed 150 characters',
        }),
    description: Joi.string().max(500).optional().messages({
        'string.max': 'Description cannot exceed 500 characters',
    }),
    image: Joi.string().optional().allow(null),
    metaTitle: Joi.string().max(60).optional().allow('').messages({
        'string.max': 'Meta title cannot exceed 60 characters',
    }),
    metaDescription: Joi.string().max(160).optional().allow('').messages({
        'string.max': 'Meta description cannot exceed 160 characters',
    }),
    articles: Joi.array()
        .items(Joi.string().pattern(/^[0-9a-fA-F]{24}$/))
        .optional()
        .messages({
            'string.pattern.base': 'Article ID must be a valid MongoDB ObjectId',
        }),
});

export const createCategorySchema = Joi.object({
    versions: Joi.object()
        .pattern(Joi.string().valid(...Object.values(Language)), categoryVersionSchema)
        .min(1)
        .required()
        .messages({
            'object.min': 'At least one language version is required',
            'any.required': 'Category versions are required',
        }),
    robotsMeta: Joi.string()
        .valid(...Object.values(robotsMeta))
        .optional()
        .default('index'),
    categoryType: Joi.string()
        .valid(...Object.values(CategoryType))
        .optional()
        .default('article'),
});

export const updateCategoryVersionSchema = Joi.object({
    language: Joi.string()
        .valid(...Object.values(Language))
        .required(),
    name: Joi.string().min(2).max(100).optional().messages({
        'string.min': 'Category name must be at least 2 characters long',
        'string.max': 'Category name cannot exceed 100 characters',
    }),
    url: Joi.string()
        .min(2)
        .max(150)
        .pattern(/^[a-z0-9-]+$/)
        .optional()
        .messages({
            'string.pattern.base': 'URL must contain only lowercase letters, numbers, and hyphens',
            'string.min': 'URL must be at least 2 characters long',
            'string.max': 'URL cannot exceed 150 characters',
        }),
    description: Joi.string().max(500).optional().messages({
        'string.max': 'Description cannot exceed 500 characters',
    }),
    image: Joi.string().optional().allow(null),
    metaTitle: Joi.string().max(60).optional().allow('').messages({
        'string.max': 'Meta title cannot exceed 60 characters',
    }),
    metaDescription: Joi.string().max(160).optional().allow('').messages({
        'string.max': 'Meta description cannot exceed 160 characters',
    }),
    articles: Joi.array()
        .items(Joi.string().pattern(/^[0-9a-fA-F]{24}$/))
        .optional()
        .messages({
            'string.pattern.base': 'Article ID must be a valid MongoDB ObjectId',
        }),
});

export const updateCategorySchema = Joi.object({
    robotsMeta: Joi.string()
        .valid(...Object.values(robotsMeta))
        .optional(),
    categoryType: Joi.string()
        .valid(...Object.values(CategoryType))
        .optional(),
})
    .min(1)
    .messages({
        'object.min': 'At least one field must be provided for update',
    });

export const validateCreateCategory = (req: any, res: any, next: any) => {
    const { error } = createCategorySchema.validate(req.body, { abortEarly: false });
    if (error) {
        const errors = error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message,
        }));
        return res.status(400).json({
            message: 'Validation failed',
            errors,
        });
    }
    next();
};

export const validateUpdateCategoryVersion = (req: any, res: any, next: any) => {
    const { error } = updateCategoryVersionSchema.validate(req.body, { abortEarly: false });
    if (error) {
        const errors = error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message,
        }));
        return res.status(400).json({
            message: 'Validation failed',
            errors,
        });
    }
    next();
};

export const validateUpdateCategory = (req: any, res: any, next: any) => {
    const { error } = updateCategorySchema.validate(req.body, { abortEarly: false });
    if (error) {
        const errors = error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message,
        }));
        return res.status(400).json({
            message: 'Validation failed',
            errors,
        });
    }
    next();
};
