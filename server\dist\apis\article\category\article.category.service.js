"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const article_category_model_1 = __importDefault(require("./article.category.model"));
const mongoose_1 = require("mongoose");
const article_model_1 = __importDefault(require("../article.model"));
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const messages_1 = require("@/utils/helpers/messages");
const bson_1 = require("bson");
const constants_1 = require("@/utils/helpers/constants");
class CategoryArticleService {
    async getSlugBySlug(language, url) {
        const category = await article_category_model_1.default
            .findOne({ [`versions.${language}.url`]: url })
            .lean()
            .exec();
        if (!category)
            throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
        const targetLanguage = language === 'en' ? 'fr' : 'en';
        const selectedVersion = category.versions[targetLanguage];
        if (!selectedVersion)
            throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
        return {
            slug: selectedVersion.url,
            name: selectedVersion.name,
        };
    }
    async createCategory(categoryData, currentUser) {
        if (!categoryData.versions || Object.keys(categoryData.versions).length === 0)
            throw new http_exception_1.default(400, messages_1.MESSAGES.CATEGORY.MISSING_VERSIONS);
        const duplicateChecks = Object.keys(categoryData.versions).map(language => ({
            [`versions.${language}.name`]: categoryData.versions[language].name?.trim(),
        }));
        const duplicateCategory = await article_category_model_1.default.findOne({
            $or: duplicateChecks,
        });
        if (duplicateCategory)
            throw new http_exception_1.default(409, messages_1.MESSAGES.CATEGORY.ALREADY_EXIST);
        const processedVersions = {};
        for (const [language, versionData] of Object.entries(categoryData.versions)) {
            const version = versionData;
            if (!version.language)
                version.language = language;
            if (!version.url && version.name)
                version.url = await this.generateUniqueUrl(version.name, language);
            version.createdAt = new Date();
            version.updatedAt = new Date();
            version._id = new mongoose_1.Types.ObjectId();
            processedVersions[language] = version;
        }
        const newCategory = new article_category_model_1.default({
            versions: processedVersions,
            robotsMeta: categoryData.robotsMeta || 'index',
            categoryType: categoryData.categoryType || 'article',
        });
        const savedCategory = await newCategory.save();
        for (const [language, version] of Object.entries(processedVersions)) {
            const versionData = version;
            if (versionData.articles && versionData.articles.length > 0)
                await this.syncCategoryArticles(versionData._id, versionData.articles, []);
        }
        return savedCategory;
    }
    async getAllCategories(queries) {
        const { paginated = 'true', pageNumber = 1, pageSize = 10, language, sortOrder = 'desc', name } = queries;
        const queryConditions = {};
        if (language)
            queryConditions[`versions.${language}`] = { $exists: true };
        if (name)
            queryConditions[`versions.${language}.name`] = new RegExp(name, 'i');
        let categoriesQuery = article_category_model_1.default.find(queryConditions);
        if (sortOrder)
            categoriesQuery = categoriesQuery.sort({ createdAt: sortOrder === 'asc' ? 1 : -1 });
        if (paginated === 'true') {
            const skip = (pageNumber - 1) * pageSize;
            categoriesQuery = categoriesQuery.skip(skip).limit(pageSize);
        }
        const categories = await categoriesQuery.lean();
        const filteredCategories = categories.map(category => {
            const versions = {};
            if (language) {
                if (category.versions[language]) {
                    versions[language] = category.versions[language];
                }
            }
            else {
                Object.assign(versions, category.versions);
            }
            return {
                _id: category._id,
                versions: versions,
                robotsMeta: category.robotsMeta,
                categoryType: category.categoryType,
            };
        });
        const totalCategories = await article_category_model_1.default.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalCategories / pageSize);
        return {
            pageNumber: Number(pageNumber),
            pageSize: Number(pageSize),
            totalPages,
            totalCategories,
            categoriesData: filteredCategories,
        };
    }
    async getAllCategoriesList(queries) {
        const { paginated: paginatedStr = 'true', pageNumber: pageNumberStr = '1', pageSize: pageSizeStr = '10', language, sortOrder = 'desc', name, } = queries;
        const paginated = paginatedStr === 'true';
        const pageNumber = parseInt(pageNumberStr, 10) || 1;
        const pageSize = parseInt(pageSizeStr, 10) || 10;
        const skip = (pageNumber - 1) * pageSize;
        const queryConditions = {};
        if (language) {
            queryConditions[`versions.${language}`] = { $exists: true };
            if (name) {
                queryConditions[`versions.${language}.name`] = new RegExp(name, 'i');
            }
        }
        const projection = {
            _id: 1,
            robotsMeta: 1,
            versions: language ? { [language]: 1 } : 1,
        };
        const categoriesQuery = article_category_model_1.default
            .find(queryConditions)
            .select(projection)
            .sort({ createdAt: sortOrder === 'asc' ? 1 : -1 })
            .lean();
        if (paginated)
            categoriesQuery.skip(skip).limit(pageSize);
        const totalCategoriesQuery = article_category_model_1.default.countDocuments(queryConditions);
        const [categoriesData, totalCategories] = await Promise.all([categoriesQuery.exec(), totalCategoriesQuery.exec()]);
        let result = { totalCategories, categoriesData };
        if (paginated) {
            const totalPages = Math.ceil(totalCategories / pageSize);
            result = {
                pageSize,
                pageNumber,
                totalPages,
                totalCategories,
                categoriesData,
            };
        }
        return result;
    }
    async getCategoriesByLanguage(language) {
        const categories = await article_category_model_1.default.find({ [`versions.${language}`]: { $exists: true } }).lean();
        return categories
            .map(category => {
            const version = category.versions[language];
            if (!version)
                return null;
            return {
                _id: category._id,
                versions: {
                    [language]: {
                        name: version.name,
                        id: version._id,
                    },
                },
            };
        })
            .filter(Boolean);
    }
    async addVersionToCategory(categoryId, newVersion) {
        const existingCategory = await article_category_model_1.default.findById(categoryId);
        if (!existingCategory)
            throw new http_exception_1.default(404, 'Category not found');
        const existingUrls = new Set();
        const existingCategories = await article_category_model_1.default.find({});
        existingCategories.forEach(category => {
            Object.values(category.versions).forEach((version) => {
                if (version.url) {
                    existingUrls.add(version.url);
                }
            });
        });
        if (!newVersion.url)
            newVersion.url = `${newVersion.name.toLowerCase().replace(/\s+/g, '-')}`;
        let tempUrl = newVersion.url;
        let count = 1;
        while (existingUrls.has(tempUrl)) {
            tempUrl = `${newVersion.url}-${count}`;
            count++;
        }
        newVersion.url = tempUrl;
        existingCategory.versions[newVersion.language] = newVersion;
        const updatedCategory = await existingCategory.save();
        return updatedCategory;
    }
    async getCategoryByUrl(language, url) {
        const category = await article_category_model_1.default
            .findOne({ [`versions.${language}.url`]: url.toLowerCase() })
            .lean()
            .exec();
        if (!category)
            throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
        const articles = await article_model_1.default
            .find({ 'versions._id': { $in: category.versions[language].articles } })
            .select({ 'versions.title': 1, 'versions.url': 1 })
            .lean()
            .exec();
        category.versions[language].articles = articles;
        return {
            _id: category._id,
            versions: category.versions[language],
            robotsMeta: category.robotsMeta || '',
        };
    }
    async getCategory(id) {
        try {
            const category = await article_category_model_1.default.findById(id).lean();
            if (!category)
                throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
            return category;
        }
        catch (error) {
            throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    async getOppositeLanguageVersionsCategory(language, versionIds) {
        const targetLanguage = language === 'en' ? 'fr' : 'en';
        const categories = await article_category_model_1.default
            .find({
            $or: [{ [`versions.${language}._id`]: { $in: versionIds } }, { [`versions.${targetLanguage}._id`]: { $in: versionIds } }],
        })
            .exec();
        if (!categories || categories.length === 0) {
            return [];
        }
        const filteredCategories = categories.map(category => {
            const targetVersion = category.versions[targetLanguage];
            if (targetVersion) {
                return {
                    _id: targetVersion._id,
                    name: targetVersion.name,
                };
            }
            else {
                return {
                    _id: null,
                    name: 'N/A',
                };
            }
        });
        return filteredCategories;
    }
    async convertToNewModel(file) {
        const legacyCategories = bson_1.BSON.EJSON.parse(file.buffer.toString());
        const convertedCategories = [];
        for (const legacy of legacyCategories) {
            const versions = {};
            if (Array.isArray(legacy.versionscategory)) {
                for (const version of legacy.versionscategory) {
                    const lang = version.language;
                    if (lang) {
                        if (version.articles && typeof version.articles === 'string') {
                            try {
                                const parsedArray = JSON.parse(version.articles.replace(/'/g, '"'));
                                if (Array.isArray(parsedArray)) {
                                    version.articles = parsedArray.map(item => item['$oid'] || item);
                                }
                            }
                            catch (e) {
                                console.error("Failed to parse 'articles' string:", e);
                                version.articles = [];
                            }
                        }
                        versions[lang] = {
                            ...version,
                            language: lang,
                        };
                    }
                }
            }
            delete legacy.versionscategory;
            console.log({
                ...legacy,
                versions,
            });
            const newCategory = new article_category_model_1.default({
                ...legacy,
                versions,
            });
            const saved = await newCategory.save();
            convertedCategories.push(saved);
        }
        return convertedCategories;
    }
    async upsertCategoryVersion(categoryId, language, versionData, currentUser) {
        const category = await article_category_model_1.default.findById(categoryId);
        if (!category)
            throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
        if (!Object.values(constants_1.Language).includes(language))
            throw new http_exception_1.default(400, messages_1.MESSAGES.CATEGORY.INVALID_LANGUAGE);
        if (versionData.url) {
            const existingCategory = await article_category_model_1.default.findOne({
                _id: { $ne: categoryId },
                [`versions.${language}.url`]: versionData.url,
            });
            if (existingCategory) {
                throw new http_exception_1.default(409, messages_1.MESSAGES.CATEGORY.DUPLICATE_URL);
            }
        }
        if (!versionData.url && versionData.name) {
            versionData.url = await this.generateUniqueUrl(versionData.name, language, categoryId);
        }
        const existingVersion = category.versions[language];
        const updatedVersion = {
            language,
            ...versionData,
            updatedAt: new Date(),
            _id: existingVersion?._id || new mongoose_1.Types.ObjectId(),
        };
        category.versions[language] = updatedVersion;
        const savedCategory = await category.save();
        if (versionData.articles) {
            const previousArticles = existingVersion?.articles || [];
            await this.syncCategoryArticles(updatedVersion._id, versionData.articles, previousArticles);
        }
        return savedCategory;
    }
    async updateCategory(categoryId, updateData, currentUser) {
        const category = await article_category_model_1.default.findById(categoryId);
        if (!category) {
            throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
        }
        const allowedGlobalFields = ['robotsMeta', 'categoryType'];
        const globalUpdates = {};
        allowedGlobalFields.forEach(field => {
            if (updateData[field] !== undefined) {
                globalUpdates[field] = updateData[field];
            }
        });
        Object.assign(category, globalUpdates);
        const savedCategory = await category.save();
        return savedCategory;
    }
    async generateUniqueUrl(name, language, excludeCategoryId) {
        const baseUrl = name
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim();
        let url = baseUrl;
        let counter = 0;
        while (true) {
            const query = { [`versions.${language}.url`]: url };
            if (excludeCategoryId) {
                query._id = { $ne: excludeCategoryId };
            }
            const existingCategory = await article_category_model_1.default.findOne(query);
            if (!existingCategory) {
                return url;
            }
            counter++;
            url = `${baseUrl}-${counter}`;
        }
    }
    async syncCategoryArticles(categoryVersionId, newArticleIds, previousArticleIds) {
        try {
            const previousIds = previousArticleIds.map(id => id.toString());
            const addedArticles = newArticleIds.filter(id => !previousIds.includes(id));
            const removedArticles = previousIds.filter(id => !newArticleIds.includes(id));
            for (const articleId of addedArticles) {
                const article = await article_model_1.default.findById(articleId);
                if (article) {
                    const updatePromises = [];
                    for (const [language, version] of Object.entries(article.versions || {})) {
                        const versionData = version;
                        if (versionData && versionData.category && !versionData.category.includes(categoryVersionId)) {
                            updatePromises.push(article_model_1.default.updateOne({ _id: articleId }, { $addToSet: { [`versions.${language}.category`]: categoryVersionId } }));
                        }
                    }
                    await Promise.all(updatePromises);
                }
            }
            for (const articleId of removedArticles) {
                const article = await article_model_1.default.findById(articleId);
                if (article) {
                    const updatePromises = [];
                    for (const [language, version] of Object.entries(article.versions || {})) {
                        const versionData = version;
                        if (versionData && versionData.category) {
                            updatePromises.push(article_model_1.default.updateOne({ _id: articleId }, { $pull: { [`versions.${language}.category`]: categoryVersionId } }));
                        }
                    }
                    await Promise.all(updatePromises);
                }
            }
        }
        catch (error) {
            console.error('Error syncing category articles:', error);
            throw new http_exception_1.default(500, messages_1.MESSAGES.CATEGORY.ARTICLES_SYNC_ERROR);
        }
    }
}
exports.default = CategoryArticleService;
//# sourceMappingURL=article.category.service.js.map