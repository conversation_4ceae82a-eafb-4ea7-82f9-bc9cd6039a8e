"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_locales_en_validations_json"],{

/***/ "(app-pages-browser)/./src/locales/en/validations.json":
/*!*****************************************!*\
  !*** ./src/locales/en/validations.json ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

module.exports = /*#__PURE__*/JSON.parse('{"invalidEmail":"Invalid email","emptyField":"Please fill in this required field !","invalidDate":"Invalid date","endDate":"End date must be after start date","minDate":"Date of birth must be after 1950","maxDate":"Date must be before 2005","minLength":"Field must be at least 3 characters","maxLength":"Field must be at most 20 characters","required":"This field is required!","invalidPassword":"Password requires at least one uppercase, one lowercase letter, one digit, and one special character","passwordMatch":"password must match","minOne":"At least one skill is required","minNationality":"At least one nationality is required","minRoles":"At least one role is required","phoneFormat":"Invalid phone number format","companyEmailRequired":"Please use your company email address","titleRequired":"Title is required","titleMinLength":"Title must be at least 3 characters long","titleMaxLength":"Title must not exceed 200 characters","urlRequired":"URL is required","urlInvalidFormat":"URL must contain only lowercase letters, numbers, and hyphens","urlMinLength":"URL must be at least 3 characters long","urlMaxLength":"URL must not exceed 100 characters","metaTitleRequired":"Meta title is required","metaTitleMinLength":"Meta title must be at least 10 characters long","metaTitleMaxLength":"Meta title must not exceed 60 characters","metaDescriptionRequired":"Meta description is required","metaDescriptionMinLength":"Meta description must be at least 50 characters long","metaDescriptionMaxLength":"Meta description must not exceed 160 characters","descriptionRequired":"Description is required","descriptionMinLength":"Description must be at least 20 characters long","descriptionMaxLength":"Description must not exceed 500 characters","contentRequired":"Content is required","contentMinLength":"Content must be at least 100 characters long","contentNotEmpty":"Content cannot be empty or contain only HTML tags","altRequired":"Alt text is required","altMinLength":"Alt text must be at least 5 characters long","altMaxLength":"Alt text must not exceed 125 characters","visibilityRequired":"Visibility is required","visibilityInvalid":"Invalid visibility option","publishDateRequired":"Publish date is required for public articles","categoryRequired":"At least one category is required","categoryMaxLimit":"Maximum 5 categories allowed","keywordsRequired":"At least one keyword is required","keywordsMaxLimit":"Maximum 10 keywords allowed","highlightsMaxLimit":"Maximum 5 highlights allowed","canonicalInvalidUrl":"Canonical URL must be a valid URL","faqTitleMaxLength":"FAQ title must not exceed 100 characters","faqQuestionRequired":"FAQ question is required","faqQuestionMinLength":"FAQ question must be at least 10 characters long","faqQuestionMaxLength":"FAQ question must not exceed 200 characters","faqAnswerRequired":"FAQ answer is required","faqAnswerMinLength":"FAQ answer must be at least 20 characters long","faqAnswerMaxLength":"FAQ answer must not exceed 1000 characters","faqMaxLimit":"Maximum 10 FAQ items allowed","ctaBannerLinkInvalidUrl":"CTA banner link must be a valid URL","robotsMetaRequired":"Robots meta is required","robotsMetaInvalid":"Invalid robots meta option","tagsMaxLimit":"Maximum 10 tags allowed","selectAtLeastOneLanguage":"Please select at least one language","imageRequired":"Image is required","ctaBannerIncomplete":"CTA banner requires both image and link","pleaseFixErrors":"Please fix the validation errors before submitting","noValidVersions":"No valid language versions found"}');

/***/ })

}]);