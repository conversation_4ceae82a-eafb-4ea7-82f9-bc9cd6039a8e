"use client";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useRouter } from "next/navigation";
import { toast } from "react-toastify";
import Svgedit from "@/assets/images/icons/edit-icon.svg";
import { Grid, InputBase, Select, MenuItem } from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import { axiosGetJson } from "@/config/axios";
import { API_URLS } from "@/utils/urls";
import Loading from "@/components/loading/Loading";
import { formatDate } from "@/utils/functions";
import CustomButton from "@/components/ui/CustomButton";
import { useTheme, useMediaQuery } from "@mui/material";
import { adminRoutes, baseUrlBackoffice } from "@/helpers/routesList";
import CustomFilters from "@/components/ui/CustomFilters";

import Svgdelete from "@/assets/images/delete.svg";
import DialogModal from "@/features/user/component/updateProfile/experience/DialogModal";
import { DeleteAllCategory } from "../services/category.service";

const ListCategory = ({ language }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const router = useRouter();
  const { t } = useTranslation();
  const [action, setAction] = useState("");
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortOrder, setSortOrder] = useState("desc");
  const [categoriesData, setCategoriesData] = useState([]);
  const [selectedLanguage, setSelectedLanguage] = useState(
    language ? language : "en"
  );
  const [loading, setLoading] = useState(false);
  const [paginationModel, setPaginationModel] = React.useState({
    page: 0,
    pageSize: 10,
  });
  const [search, setSearch] = useState(false);

  const resetSearch = () => {
    setSortOrder("desc");
    setSearchQuery("");
    setSelectedLanguage(language ? language : "en");
    setPageNumber(1);
    setSearch(!search);
  };

  const filters = [
    {
      type: "text",
      label: "Search by title ",
      value: searchQuery,
      onChange: (e) => setSearchQuery(e.target.value),
      placeholder: "Search",
    },
    {
      type: "select",
      label: t("global:sort"),
      value: sortOrder
        ? {
            value: sortOrder,
            label: t(sortOrder === "desc" ? "global:newest" : "global:oldest"),
          }
        : null,
      onChange: (e, val) => setSortOrder(val?.value || ""),
      options: [
        { value: "desc", label: t("global:newest") },
        { value: "asc", label: t("global:oldest") },
      ],
      condition: true,
    },
    {
      type: "select",
      label: "Language",
      value: selectedLanguage,
      onChange: (e, val) => setSelectedLanguage(val ? val.toLowerCase() : ""),
      options: ["EN", "FR"],
      condition: true,
    },
  ];

  const fetchCategories = async () => {
    setLoading(true);
    try {
      const response = await axiosGetJson.get(`${API_URLS.categories}`, {
        params: {
          language: selectedLanguage,
          pageSize: paginationModel.pageSize,
          pageNumber: paginationModel.page + 1,
          sortOrder,
          name: searchQuery,
        },
      });
      setCategoriesData(response?.data?.categoriesData);
      setTotalPages(response?.data?.totalCategories);
    } catch (error) {
      toast.error(t("messages:fetchCategoriesFailed"));
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchCategories();
  }, [search, paginationModel]);

  const handleEdit = (id) => {
    if (id) {
      router.push(`edit/${id}`);
    } else {
      toast.error(t("messages:idNotFound"));
    }
  };

  const handleSearchClick = () => {
    setSearch(!search);
  };

  const handleChange = (item, event) => {
    setAction(event.target.value);
    switch (event.target.value) {
      case "edit":
        handleEdit(item.id);
        break;
      case "delete":
        handleDeleteClick(item);
        break;
      default:
        break;
    }
  };

  const handleDeleteClick = (category) => {
    setCategoryToDelete(category);
    setOpenDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setCategoryToDelete(null);
  };

  const handleConfirmDelete = async () => {
    if (categoryToDelete) {
      try {
        console.log("Deleting category:", {
          categoryToDelete,
          selectedLanguage,
          originalItem: categoryToDelete.originalItem,
        });

        // Double-check that the category has the selected language version
        const originalCategory = categoriesData.find(
          (cat) => cat._id === categoryToDelete.id
        );
        if (originalCategory) {
          console.log("Original category data:", originalCategory);
          console.log(
            "Available versions:",
            Object.keys(originalCategory.versions || {})
          );

          if (!originalCategory.versions?.[selectedLanguage]) {
            toast.error(
              `Category does not have a ${selectedLanguage.toUpperCase()} version. Available versions: ${Object.keys(
                originalCategory.versions || {}
              )
                .join(", ")
                .toUpperCase()}`
            );
            setOpenDeleteDialog(false);
            setCategoryToDelete(null);
            return;
          }
        }

        await DeleteAllCategory({
          language: selectedLanguage,
          id: categoryToDelete.id,
        });
        setOpenDeleteDialog(false);
        setCategoryToDelete(null);
        fetchCategories();
      } catch (error) {
        console.error("Error deleting category:", error);
        const errorMessage =
          error?.response?.data?.message || "Error deleting category";
        toast.error(errorMessage);
      }
    }
  };

  const columns = [
    {
      field: "name",
      headerName: t("listCategory:name"),
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell",
      flex: 1,

      renderCell: (params) => (
        <a
          href={`/${params.row.language}/blog/category/${params.row?.url}`}
          className="link"
        >
          {params.row.name}
        </a>
      ),
    },
    {
      field: "url",
      headerName: t("listCategory:url"),
      headerClassName: "datagrid-header ",
      cellClassName: "datagrid-cell  ",

      flex: 1,
    },
    {
      field: "createdAt",
      headerName: t("listCategory:dateOfCreation"),
      headerClassName: "datagrid-header ",
      cellClassName: "datagrid-cell  ",

      flex: 1,
    },
    {
      field: "categoryType",
      headerName: t("Category Type"),
      headerClassName: "datagrid-header ",
      cellClassName: "datagrid-cell  ",

      flex: 1,
    },
    {
      field: "actions",
      headerName: "",
      renderCell: (params) => (
        <Select
          value={action}
          onChange={(e) => handleChange(params.row, e)}
          displayEmpty
          input={<InputBase />}
          style={{ width: "100%" }}
          renderValue={() => t("listArticle:Actions")}
        >
          <MenuItem value="edit">
            <Svgedit style={{ marginRight: 8 }} />
            {t("global:edit")}
          </MenuItem>
          <MenuItem value="delete">
            <Svgdelete style={{ marginRight: 8 }} />
            {t("global:delete")}
          </MenuItem>
        </Select>
      ),
      flex: 1,
    },
  ];

  const rows =
    categoriesData
      ?.map((item) => {
        console.log("Processing category item:", {
          id: item._id,
          selectedLanguage,
          versions: item?.versions,
          availableVersions: Object.keys(item?.versions || {}),
        });

        const version = item?.versions?.[selectedLanguage];
        if (!version) {
          console.log(
            `Skipping category ${item._id} - no ${selectedLanguage} version`
          );
          return null;
        }

        console.log(
          `Including category ${item._id} with ${selectedLanguage} version`
        );
        return {
          id: item._id,
          name: version?.name,
          url: version?.url,
          createdAt: formatDate(version?.createdAt),
          language: version?.language,
          link: version?.link || "",
          categoryType: item.categoryType,
          originalItem: item,
        };
      })
      .filter(Boolean) || [];

  if (loading) {
    return <Loading />;
  }

  return (
    <>
      <div className="display-inline">
        <p className="heading-h2 semi-bold">
          {t("listCategory:listCategory")}{" "}
          <span className="opportunities-nbr">{totalPages}</span>
        </p>

        <CustomButton
          className="btn btn-filled"
          text={t("global:addcategorie")}
          link={`/${baseUrlBackoffice.baseURL.route}/${adminRoutes.categories.route}/${adminRoutes.add.route}/`}
        />
      </div>

      <div id="container" className="recent-application-pentabell">
        <div className={`main-content`}>
          <div className="">
            <Grid container className="flex" spacing={3}>
              <Grid item xs={12}>
                <CustomFilters
                  filters={filters}
                  onSearch={handleSearchClick}
                  onReset={resetSearch}
                  searchLabel={t("search")}
                />
              </Grid>
              <Grid item xs={12}>
                <div style={{ height: "100%", width: "100%" }}>
                  <DataGrid
                    rows={rows}
                    columns={columns}
                    pagination
                    paginationMode="server"
                    paginationModel={paginationModel}
                    onPaginationModelChange={setPaginationModel}
                    pageSizeOptions={[5, 10, 25]}
                    rowCount={totalPages || [0]}
                    autoHeight
                    className="pentabell-table"
                    disableSelectionOnClick
                    columnVisibilityModel={{
                      createdAt: !isMobile,
                      url: !isMobile,
                    }}
                  />
                </div>
              </Grid>
            </Grid>
          </div>
        </div>
      </div>

      <DialogModal
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        message={t("messages:deleteCategory")}
        icon={<Svgdelete />}
      />
    </>
  );
};

export default ListCategory;
