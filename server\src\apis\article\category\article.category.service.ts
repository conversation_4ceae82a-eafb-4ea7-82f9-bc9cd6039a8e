import categoryModel from './article.category.model';
import { CategoryI } from './article.category.interfaces';
import { ProjectionType, Types } from 'mongoose';
import articleModel from '../article.model';
import HttpException from '@/utils/exceptions/http.exception';
import { MESSAGES } from '@/utils/helpers/messages';
import { FilterQuery } from 'mongoose';
import { BSON } from 'bson';
import { Language } from '@/utils/helpers/constants';

class CategoryArticleService {
    public async getSlugBySlug(language: string, url: string): Promise<{ slug: string; name: string }> {
        const category = await categoryModel
            .findOne({ [`versions.${language}.url`]: url })
            .lean()
            .exec();
        if (!category) throw new HttpException(404, MESSAGES.CATEGORY.NOT_FOUND);
        const targetLanguage = language === 'en' ? 'fr' : 'en';
        const selectedVersion = category.versions[targetLanguage];
        if (!selectedVersion) throw new HttpException(404, MESSAGES.CATEGORY.NOT_FOUND);
        return {
            slug: selectedVersion.url,
            name: selectedVersion.name,
        };
    }

    public async createCategory(categoryData: any, currentUser?: any): Promise<CategoryI> {
        if (!categoryData.versions || Object.keys(categoryData.versions).length === 0)
            throw new HttpException(400, MESSAGES.CATEGORY.MISSING_VERSIONS);

        const duplicateChecks = Object.keys(categoryData.versions).map(language => ({
            [`versions.${language}.name`]: categoryData.versions[language].name?.trim(),
        }));

        const duplicateCategory = await categoryModel.findOne({
            $or: duplicateChecks,
        });

        if (duplicateCategory) throw new HttpException(409, MESSAGES.CATEGORY.ALREADY_EXIST);

        const processedVersions: { [key: string]: any } = {};

        for (const [language, versionData] of Object.entries(categoryData.versions)) {
            const version = versionData as any;

            if (!version.language) version.language = language;
            if (!version.url && version.name) version.url = await this.generateUniqueUrl(version.name, language);

            version.createdAt = new Date();
            version.updatedAt = new Date();
            version._id = new Types.ObjectId();

            processedVersions[language] = version;
        }

        const newCategory = new categoryModel({
            versions: processedVersions,
            robotsMeta: categoryData.robotsMeta || 'index',
            categoryType: categoryData.categoryType || 'article',
        });

        const savedCategory = await newCategory.save();

        for (const [language, version] of Object.entries(processedVersions)) {
            const versionData = version as any;
            if (versionData.articles && versionData.articles.length > 0) await this.syncCategoryArticles(versionData._id, versionData.articles, []);
        }

        return savedCategory;
    }

    public async getAllCategories(queries: any): Promise<any> {
        const { paginated = 'true', pageNumber = 1, pageSize = 10, language, sortOrder = 'desc', name } = queries;

        const queryConditions: FilterQuery<CategoryI> = {};
        if (language) queryConditions[`versions.${language}`] = { $exists: true };

        if (name) queryConditions[`versions.${language}.name`] = new RegExp(name, 'i');

        let categoriesQuery = categoryModel.find(queryConditions);

        if (sortOrder) categoriesQuery = categoriesQuery.sort({ createdAt: sortOrder === 'asc' ? 1 : -1 });

        if (paginated === 'true') {
            const skip = (pageNumber - 1) * pageSize;
            categoriesQuery = categoriesQuery.skip(skip).limit(pageSize);
        }

        const categories = await categoriesQuery.lean();

        const filteredCategories = categories.map(category => {
            const versions: any = {};
            if (language) {
                if (category.versions[language]) {
                    versions[language] = category.versions[language];
                }
            } else {
                Object.assign(versions, category.versions);
            }

            return {
                _id: category._id,
                versions: versions,
                robotsMeta: category.robotsMeta,
                categoryType: category.categoryType,
            };
        });

        const totalCategories = await categoryModel.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalCategories / pageSize);

        return {
            pageNumber: Number(pageNumber),
            pageSize: Number(pageSize),
            totalPages,
            totalCategories,
            categoriesData: filteredCategories,
        };
    }

    public async getAllCategoriesList(queries: any): Promise<{ categoriesData: any[]; totalCategories: number }> {
        const {
            paginated: paginatedStr = 'true',
            pageNumber: pageNumberStr = '1',
            pageSize: pageSizeStr = '10',
            language,
            sortOrder = 'desc',
            name,
        } = queries;
        interface CategoryResponseI {
            categoriesData: CategoryI[];
            totalCategories: number;
            totalPages?: number;
            pageSize?: number;
            pageNumber?: number;
        }

        const paginated = paginatedStr === 'true';
        const pageNumber = parseInt(pageNumberStr, 10) || 1;
        const pageSize = parseInt(pageSizeStr, 10) || 10;
        const skip = (pageNumber - 1) * pageSize;

        const queryConditions: FilterQuery<CategoryI> = {};
        if (language) {
            queryConditions[`versions.${language}`] = { $exists: true };
            if (name) {
                queryConditions[`versions.${language}.name`] = new RegExp(name, 'i');
            }
        }

        const projection: ProjectionType<CategoryI> = {
            _id: 1,
            robotsMeta: 1,
            versions: language ? { [language]: 1 } : 1,
        };

        const categoriesQuery = categoryModel
            .find(queryConditions)
            .select(projection)
            .sort({ createdAt: sortOrder === 'asc' ? 1 : -1 })
            .lean();

        if (paginated) categoriesQuery.skip(skip).limit(pageSize);

        const totalCategoriesQuery = categoryModel.countDocuments(queryConditions);
        const [categoriesData, totalCategories] = await Promise.all([categoriesQuery.exec(), totalCategoriesQuery.exec()]);

        let result: CategoryResponseI = { totalCategories, categoriesData };

        if (paginated) {
            const totalPages = Math.ceil(totalCategories / pageSize);
            result = {
                pageSize,
                pageNumber,
                totalPages,
                totalCategories,
                categoriesData,
            };
        }

        return result;
    }

    public async getCategoriesByLanguage(language: string): Promise<any[]> {
        const categories = await categoryModel.find({ [`versions.${language}`]: { $exists: true } }).lean();

        return categories
            .map(category => {
                const version = category.versions[language];
                if (!version) return null;

                return {
                    _id: category._id,
                    versions: {
                        [language]: {
                            name: version.name,
                            id: version._id,
                        },
                    },
                };
            })
            .filter(Boolean);
    }

    public async addVersionToCategory(categoryId: string, newVersion: any): Promise<any> {
        const existingCategory = await categoryModel.findById(categoryId);
        if (!existingCategory) throw new HttpException(404, 'Category not found');

        const existingUrls: Set<string> = new Set();
        const existingCategories = await categoryModel.find({});
        existingCategories.forEach(category => {
            Object.values(category.versions).forEach((version: any) => {
                if (version.url) {
                    existingUrls.add(version.url);
                }
            });
        });

        if (!newVersion.url) newVersion.url = `${newVersion.name.toLowerCase().replace(/\s+/g, '-')}`;

        let tempUrl = newVersion.url;
        let count = 1;

        while (existingUrls.has(tempUrl)) {
            tempUrl = `${newVersion.url}-${count}`;
            count++;
        }
        newVersion.url = tempUrl;

        existingCategory.versions[newVersion.language] = newVersion;

        const updatedCategory = await existingCategory.save();

        return updatedCategory;
    }

    public async getCategoryByUrl(language: string, url: string): Promise<any> {
        const category: any = await categoryModel
            .findOne({ [`versions.${language}.url`]: url.toLowerCase() })
            .lean()
            .exec();

        if (!category) throw new HttpException(404, MESSAGES.CATEGORY.NOT_FOUND);
        const articles = await articleModel
            .find({ 'versions._id': { $in: category.versions[language].articles } })
            .select({ 'versions.title': 1, 'versions.url': 1 })
            .lean()
            .exec();
        category.versions[language].articles = articles;

        return {
            _id: category._id,
            versions: category.versions[language],
            robotsMeta: category.robotsMeta || '',
        };
    }

    public async getCategory(id: string): Promise<any> {
        try {
            const category = await categoryModel.findById(id).lean();
            if (!category) throw new HttpException(404, MESSAGES.CATEGORY.NOT_FOUND);
            return category;
        } catch (error) {
            throw new HttpException(500, MESSAGES.GENERAL.SERVER_ERROR);
        }
    }

    public async getOppositeLanguageVersionsCategory(language: string, versionIds: string[]): Promise<any[]> {
        const targetLanguage = language === 'en' ? 'fr' : 'en';
        const categories = await categoryModel
            .find({
                $or: [{ [`versions.${language}._id`]: { $in: versionIds } }, { [`versions.${targetLanguage}._id`]: { $in: versionIds } }],
            })
            .exec();

        if (!categories || categories.length === 0) {
            return [];
        }

        const filteredCategories = categories.map(category => {
            const targetVersion = category.versions[targetLanguage];

            if (targetVersion) {
                return {
                    _id: targetVersion._id,
                    name: targetVersion.name,
                };
            } else {
                return {
                    _id: null,
                    name: 'N/A',
                };
            }
        });

        return filteredCategories;
    }

    public async convertToNewModel(file: any): Promise<any[]> {
        const legacyCategories = BSON.EJSON.parse(file.buffer.toString());
        const convertedCategories = [];

        for (const legacy of legacyCategories) {
            const versions: Record<string, any> = {};

            if (Array.isArray(legacy.versionscategory)) {
                for (const version of legacy.versionscategory) {
                    const lang = version.language;
                    if (lang) {
                        if (version.articles && typeof version.articles === 'string') {
                            try {
                                const parsedArray = JSON.parse(version.articles.replace(/'/g, '"'));
                                if (Array.isArray(parsedArray)) {
                                    version.articles = parsedArray.map(item => item['$oid'] || item);
                                }
                            } catch (e) {
                                console.error("Failed to parse 'articles' string:", e);
                                version.articles = [];
                            }
                        }

                        versions[lang] = {
                            ...version,
                            language: lang,
                        };
                    }
                }
            }

            delete legacy.versionscategory;

            console.log({
                ...legacy,
                versions,
            });

            const newCategory = new categoryModel({
                ...legacy,
                versions,
            });

            const saved = await newCategory.save();
            convertedCategories.push(saved);
        }

        return convertedCategories;
    }

    public async upsertCategoryVersion(categoryId: string, language: Language, versionData: any, currentUser?: any): Promise<CategoryI> {
        const category = await categoryModel.findById(categoryId);
        if (!category) throw new HttpException(404, MESSAGES.CATEGORY.NOT_FOUND);

        if (!Object.values(Language).includes(language)) throw new HttpException(400, MESSAGES.CATEGORY.INVALID_LANGUAGE);

        if (versionData.url) {
            const existingCategory = await categoryModel.findOne({
                _id: { $ne: categoryId },
                [`versions.${language}.url`]: versionData.url,
            });
            if (existingCategory) {
                throw new HttpException(409, MESSAGES.CATEGORY.DUPLICATE_URL);
            }
        }

        if (!versionData.url && versionData.name) {
            versionData.url = await this.generateUniqueUrl(versionData.name, language, categoryId);
        }

        const existingVersion = (category.versions as any)[language];

        const updatedVersion = {
            language,
            ...versionData,
            updatedAt: new Date(),
            _id: existingVersion?._id || new Types.ObjectId(),
        };

        (category.versions as any)[language] = updatedVersion;
        const savedCategory = await category.save();

        if (versionData.articles) {
            const previousArticles = existingVersion?.articles || [];
            await this.syncCategoryArticles(updatedVersion._id, versionData.articles, previousArticles);
        }

        return savedCategory;
    }

    public async updateCategory(categoryId: string, updateData: any, currentUser?: any): Promise<CategoryI> {
        const category = await categoryModel.findById(categoryId);
        if (!category) {
            throw new HttpException(404, MESSAGES.CATEGORY.NOT_FOUND);
        }

        const allowedGlobalFields = ['robotsMeta', 'categoryType'];
        const globalUpdates: any = {};

        allowedGlobalFields.forEach(field => {
            if (updateData[field] !== undefined) {
                globalUpdates[field] = updateData[field];
            }
        });

        Object.assign(category, globalUpdates);

        const savedCategory = await category.save();
        return savedCategory;
    }

    private async generateUniqueUrl(name: string, language: string, excludeCategoryId?: string): Promise<string> {
        const baseUrl = name
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim();

        let url = baseUrl;
        let counter = 0;

        while (true) {
            const query: any = { [`versions.${language}.url`]: url };
            if (excludeCategoryId) {
                query._id = { $ne: excludeCategoryId };
            }

            const existingCategory = await categoryModel.findOne(query);
            if (!existingCategory) {
                return url;
            }

            counter++;
            url = `${baseUrl}-${counter}`;
        }
    }

    private async syncCategoryArticles(
        categoryVersionId: Types.ObjectId,
        newArticleIds: string[],
        previousArticleIds: Types.ObjectId[],
    ): Promise<void> {
        try {
            const previousIds = previousArticleIds.map(id => id.toString());
            const addedArticles = newArticleIds.filter(id => !previousIds.includes(id));
            const removedArticles = previousIds.filter(id => !newArticleIds.includes(id));

            // Add category to new articles
            for (const articleId of addedArticles) {
                // Find the article by its main _id
                const article = await articleModel.findById(articleId);
                if (article) {
                    // Update all versions of this article to include the category
                    const updatePromises = [];
                    for (const [language, version] of Object.entries(article.versions.toObject())) {
                        const versionData = version as any;
                        if (versionData && versionData.category && !versionData.category.includes(categoryVersionId)) {
                            updatePromises.push(
                                articleModel.updateOne({ _id: articleId }, { $addToSet: { [`versions.${language}.category`]: categoryVersionId } }),
                            );
                        }
                    }
                    await Promise.all(updatePromises);
                }
            }

            // Remove category from removed articles
            for (const articleId of removedArticles) {
                // Find the article by its main _id
                const article = await articleModel.findById(articleId);
                if (article) {
                    // Update all versions of this article to remove the category
                    const updatePromises = [];
                    for (const [language, version] of Object.entries(article.versions.toObject())) {
                        const versionData = version as any;
                        if (versionData && versionData.category) {
                            updatePromises.push(
                                articleModel.updateOne({ _id: articleId }, { $pull: { [`versions.${language}.category`]: categoryVersionId } }),
                            );
                        }
                    }
                    await Promise.all(updatePromises);
                }
            }
        } catch (error) {
            console.error('Error syncing category articles:', error);
            throw new HttpException(500, MESSAGES.CATEGORY.ARTICLES_SYNC_ERROR);
        }
    }
}

export default CategoryArticleService;
